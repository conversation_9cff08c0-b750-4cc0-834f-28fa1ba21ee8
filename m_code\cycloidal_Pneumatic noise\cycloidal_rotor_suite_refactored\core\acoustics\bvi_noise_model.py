"""
BVI噪声模型
===========

基于advice_detailed.md要求，实现完整的桨叶-涡相互作用(BVI)噪声模型：
- 桨叶-涡距离计算
- BVI事件识别
- 噪声强度估算
- 脉冲噪声特征分析
"""

import numpy as np
import warnings
from typing import Dict, Any, Optional, Tuple, List
from dataclasses import dataclass
from scipy.signal import find_peaks


@dataclass
class BVIEvent:
    """BVI事件数据结构"""
    time: float
    blade_position: np.ndarray
    vortex_position: np.ndarray
    distance: float
    relative_velocity: float
    interaction_strength: float
    noise_amplitude: float


@dataclass
class VortexElement:
    """涡元数据结构"""
    position: np.ndarray
    strength: float
    core_radius: float
    age: float


class BVINoiseModel:
    """桨叶-涡相互作用噪声模型"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化BVI噪声模型
        
        Args:
            config: 配置参数
        """
        self.config = config
        
        # BVI检测参数
        self.bvi_threshold_distance = config.get('bvi_threshold_distance', 0.1)  # 检测阈值距离 [m]
        self.min_vortex_strength = config.get('min_vortex_strength', 0.1)  # 最小涡强度
        self.interaction_time_window = config.get('interaction_time_window', 0.01)  # 相互作用时间窗口 [s]
        
        # 噪声计算参数
        self.reference_pressure = config.get('reference_pressure', 2e-5)  # 参考声压 [Pa]
        self.observer_distance = config.get('observer_distance', 10.0)  # 观测距离 [m]
        self.sound_speed = config.get('sound_speed', 343.0)  # 声速 [m/s]
        
        # 循环翼特殊参数
        self.cycloidal_correction_factor = config.get('cycloidal_correction_factor', 1.2)
        self.enable_cycloidal_corrections = config.get('enable_cycloidal_corrections', True)
        
        # BVI事件历史
        self.bvi_events = []
        self.noise_history = []
        
        print(f"✅ BVI噪声模型初始化完成")
        print(f"   BVI检测阈值: {self.bvi_threshold_distance:.3f} m")
        print(f"   循环翼修正: {'启用' if self.enable_cycloidal_corrections else '禁用'}")
    
    def detect_bvi_events(self, blade_positions: np.ndarray, 
                         wake_vortices: List[VortexElement],
                         time: float) -> List[BVIEvent]:
        """
        检测桨叶-涡相互作用事件
        
        Args:
            blade_positions: 桨叶位置数组 [N_blades, 3]
            wake_vortices: 尾迹涡元列表
            time: 当前时间
            
        Returns:
            bvi_events: 检测到的BVI事件列表
        """
        bvi_events = []
        
        for i, blade_pos in enumerate(blade_positions):
            for j, vortex in enumerate(wake_vortices):
                # 计算桨叶-涡距离
                distance = self._calculate_blade_vortex_distance(blade_pos, vortex.position)
                
                # 检查是否满足BVI条件
                if (distance < self.bvi_threshold_distance and 
                    abs(vortex.strength) > self.min_vortex_strength):
                    
                    # 计算相互作用强度
                    interaction_strength = self._calculate_interaction_strength(
                        distance, vortex.strength, vortex.core_radius
                    )
                    
                    # 估算相对速度（简化）
                    relative_velocity = self._estimate_relative_velocity(blade_pos, vortex.position)
                    
                    # 计算噪声幅度
                    noise_amplitude = self._calculate_bvi_noise_amplitude(
                        interaction_strength, relative_velocity, distance
                    )
                    
                    # 创建BVI事件
                    bvi_event = BVIEvent(
                        time=time,
                        blade_position=blade_pos.copy(),
                        vortex_position=vortex.position.copy(),
                        distance=distance,
                        relative_velocity=relative_velocity,
                        interaction_strength=interaction_strength,
                        noise_amplitude=noise_amplitude
                    )
                    
                    bvi_events.append(bvi_event)
        
        # 存储事件历史
        self.bvi_events.extend(bvi_events)
        
        return bvi_events
    
    def _calculate_blade_vortex_distance(self, blade_position: np.ndarray,
                                       vortex_position: np.ndarray) -> float:
        """计算桨叶-涡距离"""
        # 简化为点到点距离
        distance = np.linalg.norm(blade_position - vortex_position)
        return distance
    
    def _calculate_interaction_strength(self, distance: float, 
                                      vortex_strength: float,
                                      core_radius: float) -> float:
        """计算相互作用强度"""
        # 基于涡强度和距离的相互作用强度
        if distance < core_radius:
            # 在涡核内，使用修正的强度
            strength_factor = distance / core_radius
        else:
            # 在涡核外，使用1/r衰减
            strength_factor = core_radius / distance
        
        interaction_strength = abs(vortex_strength) * strength_factor
        
        # 循环翼修正
        if self.enable_cycloidal_corrections:
            interaction_strength *= self.cycloidal_correction_factor
        
        return interaction_strength
    
    def _estimate_relative_velocity(self, blade_position: np.ndarray,
                                  vortex_position: np.ndarray) -> float:
        """估算相对速度（简化实现）"""
        # 简化的相对速度估算
        # 实际应用中需要考虑桨叶运动速度和涡诱导速度
        
        # 基于位置估算典型的桨叶速度
        blade_radius = np.linalg.norm(blade_position[:2])
        typical_blade_speed = blade_radius * 10.0  # 假设角速度约10 rad/s
        
        return typical_blade_speed
    
    def _calculate_bvi_noise_amplitude(self, interaction_strength: float,
                                     relative_velocity: float,
                                     distance: float) -> float:
        """计算BVI噪声幅度"""
        # 基于相互作用强度和相对速度的噪声幅度模型
        # 参考Lowson的BVI噪声理论
        
        # 基础噪声幅度
        base_amplitude = interaction_strength * relative_velocity**2
        
        # 距离修正
        distance_factor = 1.0 / max(distance, 0.01)
        
        # 最终噪声幅度
        noise_amplitude = base_amplitude * distance_factor
        
        return noise_amplitude
    
    def calculate_bvi_noise_spectrum(self, bvi_events: List[BVIEvent],
                                   frequencies: np.ndarray,
                                   observer_position: np.ndarray) -> np.ndarray:
        """
        计算BVI噪声频谱
        
        Args:
            bvi_events: BVI事件列表
            frequencies: 频率数组 [Hz]
            observer_position: 观测点位置
            
        Returns:
            noise_spectrum: 噪声频谱 [Pa²/Hz]
        """
        if not bvi_events:
            return np.zeros_like(frequencies)
        
        noise_spectrum = np.zeros_like(frequencies)
        
        for event in bvi_events:
            # 计算传播距离
            propagation_distance = np.linalg.norm(
                observer_position - event.blade_position
            )
            
            # 计算BVI脉冲的频谱特征
            event_spectrum = self._calculate_bvi_pulse_spectrum(
                event, frequencies, propagation_distance
            )
            
            noise_spectrum += event_spectrum
        
        return noise_spectrum
    
    def _calculate_bvi_pulse_spectrum(self, event: BVIEvent,
                                    frequencies: np.ndarray,
                                    propagation_distance: float) -> np.ndarray:
        """计算单个BVI脉冲的频谱"""
        # BVI脉冲的典型频谱形状
        # 基于实验观测，BVI噪声在中频段有峰值
        
        pulse_spectrum = np.zeros_like(frequencies)
        
        # 特征频率（基于相对速度和距离）
        characteristic_freq = event.relative_velocity / (2 * np.pi * event.distance)
        characteristic_freq = max(characteristic_freq, 100.0)  # 最小100Hz
        
        for i, freq in enumerate(frequencies):
            # 频谱形状（类似于Gaussian）
            freq_ratio = freq / characteristic_freq
            spectral_shape = np.exp(-0.5 * (np.log(freq_ratio))**2)
            
            # 幅度计算
            amplitude = event.noise_amplitude / max(propagation_distance, 1.0)
            
            # 频率相关的衰减
            if freq > characteristic_freq * 10:
                amplitude *= (characteristic_freq * 10 / freq)**2
            
            pulse_spectrum[i] = amplitude * spectral_shape
        
        return pulse_spectrum
    
    def analyze_bvi_characteristics(self, time_window: float = 1.0) -> Dict[str, Any]:
        """
        分析BVI特征
        
        Args:
            time_window: 分析时间窗口 [s]
            
        Returns:
            bvi_analysis: BVI特征分析结果
        """
        if not self.bvi_events:
            return {'n_events': 0, 'analysis': 'No BVI events detected'}
        
        # 筛选时间窗口内的事件
        current_time = self.bvi_events[-1].time
        recent_events = [
            event for event in self.bvi_events
            if current_time - event.time <= time_window
        ]
        
        if not recent_events:
            return {'n_events': 0, 'analysis': 'No recent BVI events'}
        
        # 统计分析
        distances = [event.distance for event in recent_events]
        amplitudes = [event.noise_amplitude for event in recent_events]
        strengths = [event.interaction_strength for event in recent_events]
        
        analysis = {
            'n_events': len(recent_events),
            'time_window': time_window,
            'distance_stats': {
                'mean': np.mean(distances),
                'std': np.std(distances),
                'min': np.min(distances),
                'max': np.max(distances)
            },
            'amplitude_stats': {
                'mean': np.mean(amplitudes),
                'std': np.std(amplitudes),
                'min': np.min(amplitudes),
                'max': np.max(amplitudes)
            },
            'strength_stats': {
                'mean': np.mean(strengths),
                'std': np.std(strengths),
                'min': np.min(strengths),
                'max': np.max(strengths)
            },
            'event_rate': len(recent_events) / time_window  # 事件频率 [events/s]
        }
        
        return analysis
    
    def identify_bvi_features_from_signal(self, loading_noise: np.ndarray,
                                        time: np.ndarray) -> Dict[str, Any]:
        """
        从载荷噪声信号中识别BVI特征
        
        Args:
            loading_noise: 载荷噪声信号
            time: 时间数组
            
        Returns:
            bvi_features: BVI特征字典
        """
        # 使用峰值检测识别BVI脉冲
        prominence_threshold = np.std(loading_noise) * 2.0  # 动态阈值
        distance_threshold = len(time) // 20  # 最小间隔
        
        peaks, properties = find_peaks(
            np.abs(loading_noise),
            prominence=prominence_threshold,
            distance=distance_threshold
        )
        
        if len(peaks) == 0:
            return {'n_bvi_events': 0, 'message': 'No BVI pulses detected'}
        
        # 提取BVI特征
        bvi_times = time[peaks]
        bvi_amplitudes = loading_noise[peaks]
        bvi_prominences = properties['prominences']
        
        # 计算BVI统计特性
        mean_amplitude = np.mean(np.abs(bvi_amplitudes))
        amplitude_std = np.std(bvi_amplitudes)
        mean_interval = np.mean(np.diff(bvi_times)) if len(bvi_times) > 1 else 0
        
        return {
            'n_bvi_events': len(peaks),
            'bvi_times': bvi_times,
            'bvi_amplitudes': bvi_amplitudes,
            'bvi_prominences': bvi_prominences,
            'mean_amplitude': mean_amplitude,
            'amplitude_std': amplitude_std,
            'mean_interval': mean_interval,
            'event_rate': len(peaks) / (time[-1] - time[0]) if len(time) > 1 else 0
        }
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        return {
            'model_type': 'BVI_noise_model',
            'bvi_threshold_distance': self.bvi_threshold_distance,
            'min_vortex_strength': self.min_vortex_strength,
            'enable_cycloidal_corrections': self.enable_cycloidal_corrections,
            'total_events_detected': len(self.bvi_events),
            'observer_distance': self.observer_distance
        }
