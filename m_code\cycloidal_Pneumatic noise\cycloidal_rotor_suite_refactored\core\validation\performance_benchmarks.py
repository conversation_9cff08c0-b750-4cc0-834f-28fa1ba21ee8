"""
性能基准测试模块
================

基于advice_detailed.md要求，实现完整的性能基准测试功能：
- 求解器性能对比
- 计算时间分析
- 内存使用分析
- 收敛性能评估
"""

import time
import psutil
import numpy as np
import warnings
from typing import Dict, Any, Optional, List, Callable
from dataclasses import dataclass
import gc


@dataclass
class PerformanceMetrics:
    """性能指标数据结构"""
    cpu_time: float
    wall_time: float
    memory_usage_mb: float
    peak_memory_mb: float
    iterations: int
    convergence_rate: float
    cpu_utilization: float


class PerformanceBenchmark:
    """性能基准测试器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化性能基准测试器
        
        Args:
            config: 配置参数
        """
        self.config = config
        
        # 基准测试参数
        self.warmup_runs = config.get('warmup_runs', 3)
        self.benchmark_runs = config.get('benchmark_runs', 5)
        self.memory_sampling_interval = config.get('memory_sampling_interval', 0.1)
        
        # 性能阈值
        self.performance_thresholds = {
            'excellent_time': config.get('excellent_time_threshold', 1.0),  # 秒
            'good_time': config.get('good_time_threshold', 5.0),
            'acceptable_time': config.get('acceptable_time_threshold', 30.0),
            'max_memory_mb': config.get('max_memory_threshold', 1000.0)  # MB
        }
        
        # 结果存储
        self.benchmark_results = {}
        
        print(f"✅ 性能基准测试器初始化完成")
        print(f"   预热运行次数: {self.warmup_runs}")
        print(f"   基准测试次数: {self.benchmark_runs}")
    
    def benchmark_solver(self, solver_name: str, solver_function: Callable,
                        test_cases: List[Dict[str, Any]]) -> PerformanceMetrics:
        """
        对求解器进行性能基准测试
        
        Args:
            solver_name: 求解器名称
            solver_function: 求解器函数
            test_cases: 测试案例列表
            
        Returns:
            performance_metrics: 性能指标
        """
        print(f"\n🔧 开始 {solver_name} 性能基准测试...")
        
        # 预热运行
        print(f"   预热运行 ({self.warmup_runs} 次)...")
        for i in range(self.warmup_runs):
            try:
                for test_case in test_cases[:1]:  # 只用第一个测试案例预热
                    _ = solver_function(test_case)
                gc.collect()  # 强制垃圾回收
            except Exception as e:
                print(f"   预热运行 {i+1} 失败: {e}")
        
        # 正式基准测试
        print(f"   正式基准测试 ({self.benchmark_runs} 次)...")
        
        cpu_times = []
        wall_times = []
        memory_usages = []
        peak_memories = []
        iterations_list = []
        convergence_rates = []
        
        for run in range(self.benchmark_runs):
            print(f"     运行 {run + 1}/{self.benchmark_runs}...")
            
            # 记录初始内存
            process = psutil.Process()
            initial_memory = process.memory_info().rss / 1024 / 1024  # MB
            
            # 开始计时
            cpu_start = time.process_time()
            wall_start = time.time()
            
            # 运行测试案例
            run_iterations = 0
            run_convergence_rates = []
            peak_memory = initial_memory
            
            try:
                for test_case in test_cases:
                    # 监控内存使用
                    current_memory = process.memory_info().rss / 1024 / 1024
                    peak_memory = max(peak_memory, current_memory)
                    
                    # 运行求解器
                    result = solver_function(test_case)
                    
                    # 提取性能信息
                    if isinstance(result, dict):
                        run_iterations += result.get('iterations', 1)
                        if 'convergence_rate' in result:
                            run_convergence_rates.append(result['convergence_rate'])
                    else:
                        run_iterations += 1
                
            except Exception as e:
                print(f"     运行 {run + 1} 失败: {e}")
                continue
            
            # 结束计时
            cpu_end = time.process_time()
            wall_end = time.time()
            
            # 记录结果
            cpu_times.append(cpu_end - cpu_start)
            wall_times.append(wall_end - wall_start)
            memory_usages.append(peak_memory - initial_memory)
            peak_memories.append(peak_memory)
            iterations_list.append(run_iterations)
            
            if run_convergence_rates:
                convergence_rates.append(np.mean(run_convergence_rates))
            else:
                convergence_rates.append(1.0)  # 默认收敛率
            
            # 清理内存
            gc.collect()
        
        # 计算统计指标
        if not cpu_times:
            raise RuntimeError(f"所有 {solver_name} 基准测试运行都失败了")
        
        # 计算CPU利用率
        avg_cpu_time = np.mean(cpu_times)
        avg_wall_time = np.mean(wall_times)
        cpu_utilization = avg_cpu_time / avg_wall_time if avg_wall_time > 0 else 0.0
        
        performance_metrics = PerformanceMetrics(
            cpu_time=avg_cpu_time,
            wall_time=avg_wall_time,
            memory_usage_mb=np.mean(memory_usages),
            peak_memory_mb=np.mean(peak_memories),
            iterations=int(np.mean(iterations_list)),
            convergence_rate=np.mean(convergence_rates),
            cpu_utilization=cpu_utilization
        )
        
        # 存储结果
        self.benchmark_results[solver_name] = performance_metrics
        
        # 打印结果
        self._print_performance_summary(solver_name, performance_metrics)
        
        return performance_metrics
    
    def _print_performance_summary(self, solver_name: str, metrics: PerformanceMetrics) -> None:
        """打印性能摘要"""
        print(f"   📊 {solver_name} 性能摘要:")
        print(f"     CPU时间: {metrics.cpu_time:.3f} s")
        print(f"     墙钟时间: {metrics.wall_time:.3f} s")
        print(f"     内存使用: {metrics.memory_usage_mb:.1f} MB")
        print(f"     峰值内存: {metrics.peak_memory_mb:.1f} MB")
        print(f"     平均迭代次数: {metrics.iterations}")
        print(f"     收敛率: {metrics.convergence_rate:.3f}")
        print(f"     CPU利用率: {metrics.cpu_utilization:.1%}")
        
        # 性能等级评估
        performance_grade = self._evaluate_performance_grade(metrics)
        print(f"     性能等级: {performance_grade}")
    
    def _evaluate_performance_grade(self, metrics: PerformanceMetrics) -> str:
        """评估性能等级"""
        wall_time = metrics.wall_time
        memory_usage = metrics.memory_usage_mb
        
        # 时间评级
        if wall_time <= self.performance_thresholds['excellent_time']:
            time_grade = 'EXCELLENT'
        elif wall_time <= self.performance_thresholds['good_time']:
            time_grade = 'GOOD'
        elif wall_time <= self.performance_thresholds['acceptable_time']:
            time_grade = 'ACCEPTABLE'
        else:
            time_grade = 'POOR'
        
        # 内存评级
        if memory_usage <= self.performance_thresholds['max_memory_mb'] * 0.3:
            memory_grade = 'EXCELLENT'
        elif memory_usage <= self.performance_thresholds['max_memory_mb'] * 0.6:
            memory_grade = 'GOOD'
        elif memory_usage <= self.performance_thresholds['max_memory_mb']:
            memory_grade = 'ACCEPTABLE'
        else:
            memory_grade = 'POOR'
        
        # 综合评级
        grades = {'EXCELLENT': 4, 'GOOD': 3, 'ACCEPTABLE': 2, 'POOR': 1}
        avg_grade = (grades[time_grade] + grades[memory_grade]) / 2
        
        if avg_grade >= 3.5:
            return 'EXCELLENT'
        elif avg_grade >= 2.5:
            return 'GOOD'
        elif avg_grade >= 1.5:
            return 'ACCEPTABLE'
        else:
            return 'POOR'
    
    def compare_solvers(self, baseline_solver: str = None) -> Dict[str, Any]:
        """
        对比多个求解器的性能
        
        Args:
            baseline_solver: 基准求解器名称
            
        Returns:
            comparison_results: 对比结果
        """
        if len(self.benchmark_results) < 2:
            return {'error': 'Need at least 2 solvers for comparison'}
        
        # 选择基准求解器
        if baseline_solver is None:
            # 选择最慢的作为基准
            baseline_solver = max(
                self.benchmark_results.keys(),
                key=lambda x: self.benchmark_results[x].wall_time
            )
        
        if baseline_solver not in self.benchmark_results:
            baseline_solver = list(self.benchmark_results.keys())[0]
        
        baseline_metrics = self.benchmark_results[baseline_solver]
        comparison_results = {
            'baseline_solver': baseline_solver,
            'comparisons': {},
            'rankings': []
        }
        
        # 计算相对性能
        for solver_name, metrics in self.benchmark_results.items():
            if solver_name == baseline_solver:
                continue
            
            speedup = baseline_metrics.wall_time / metrics.wall_time
            memory_ratio = metrics.memory_usage_mb / baseline_metrics.memory_usage_mb
            efficiency_ratio = metrics.convergence_rate / baseline_metrics.convergence_rate
            
            comparison_results['comparisons'][solver_name] = {
                'speedup': speedup,
                'memory_ratio': memory_ratio,
                'efficiency_ratio': efficiency_ratio,
                'overall_score': speedup * efficiency_ratio / memory_ratio
            }
        
        # 生成排名
        all_solvers = [(name, metrics) for name, metrics in self.benchmark_results.items()]
        all_solvers.sort(key=lambda x: x[1].wall_time)  # 按时间排序
        
        for rank, (solver_name, metrics) in enumerate(all_solvers, 1):
            comparison_results['rankings'].append({
                'rank': rank,
                'solver_name': solver_name,
                'wall_time': metrics.wall_time,
                'memory_usage_mb': metrics.memory_usage_mb,
                'performance_grade': self._evaluate_performance_grade(metrics)
            })
        
        return comparison_results
    
    def generate_performance_report(self) -> str:
        """生成性能报告"""
        if not self.benchmark_results:
            return "No benchmark results available."
        
        report = "# 性能基准测试报告\n\n"
        
        # 总体统计
        report += "## 总体统计\n\n"
        report += f"- 测试求解器数量: {len(self.benchmark_results)}\n"
        report += f"- 基准测试运行次数: {self.benchmark_runs}\n"
        report += f"- 预热运行次数: {self.warmup_runs}\n\n"
        
        # 详细结果
        report += "## 详细结果\n\n"
        report += "| 求解器 | 墙钟时间(s) | CPU时间(s) | 内存使用(MB) | 迭代次数 | 性能等级 |\n"
        report += "|--------|-------------|------------|-------------|----------|----------|\n"
        
        for solver_name, metrics in self.benchmark_results.items():
            grade = self._evaluate_performance_grade(metrics)
            report += f"| {solver_name} | {metrics.wall_time:.3f} | {metrics.cpu_time:.3f} | "
            report += f"{metrics.memory_usage_mb:.1f} | {metrics.iterations} | {grade} |\n"
        
        # 性能对比
        if len(self.benchmark_results) > 1:
            comparison = self.compare_solvers()
            report += "\n## 性能对比\n\n"
            
            baseline = comparison['baseline_solver']
            report += f"基准求解器: {baseline}\n\n"
            
            for solver_name, comp_data in comparison['comparisons'].items():
                speedup = comp_data['speedup']
                memory_ratio = comp_data['memory_ratio']
                report += f"- **{solver_name}**: {speedup:.1f}x 加速, "
                report += f"{memory_ratio:.1f}x 内存使用\n"
        
        return report
    
    def get_benchmark_summary(self) -> Dict[str, Any]:
        """获取基准测试摘要"""
        if not self.benchmark_results:
            return {'error': 'No benchmark results'}
        
        # 计算统计信息
        wall_times = [m.wall_time for m in self.benchmark_results.values()]
        memory_usages = [m.memory_usage_mb for m in self.benchmark_results.values()]
        
        return {
            'total_solvers': len(self.benchmark_results),
            'fastest_time': min(wall_times),
            'slowest_time': max(wall_times),
            'avg_time': np.mean(wall_times),
            'min_memory': min(memory_usages),
            'max_memory': max(memory_usages),
            'avg_memory': np.mean(memory_usages),
            'performance_spread': max(wall_times) / min(wall_times),
            'detailed_results': {
                name: {
                    'wall_time': metrics.wall_time,
                    'memory_usage_mb': metrics.memory_usage_mb,
                    'performance_grade': self._evaluate_performance_grade(metrics)
                }
                for name, metrics in self.benchmark_results.items()
            }
        }
