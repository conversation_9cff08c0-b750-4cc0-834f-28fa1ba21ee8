"""
不确定性量化模块
================

基于advice_detailed.md要求，实现完整的不确定性量化功能：
- <PERSON>分析
- Richardson外推
- 参数敏感性分析
- 统计显著性检验
"""

import numpy as np
import warnings
from typing import Dict, Any, Optional, Tuple, List, Union, Callable
from dataclasses import dataclass
from scipy import stats
from scipy.optimize import minimize_scalar
import logging


@dataclass
class UncertaintyResult:
    """不确定性分析结果"""
    parameter_name: str
    mean_value: float
    standard_deviation: float
    confidence_interval: Tuple[float, float]
    uncertainty_percentage: float
    distribution_type: str
    samples: Optional[np.ndarray] = None


class UncertaintyQuantifier:
    """不确定性量化器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化不确定性量化器
        
        Args:
            config: 配置参数
        """
        self.monte_carlo_samples = config.get('monte_carlo_samples', 1000)
        self.confidence_level = config.get('confidence_level', 0.95)
        self.random_seed = config.get('random_seed', 42)
        self.enable_richardson_extrapolation = config.get('enable_richardson_extrapolation', True)
        
        # 设置随机种子
        np.random.seed(self.random_seed)
        
        # 日志
        self.logger = logging.getLogger(__name__)
        
        print(f"✅ 不确定性量化器初始化完成")
        print(f"   Monte Carlo样本数: {self.monte_carlo_samples}")
        print(f"   置信水平: {self.confidence_level}")
        print(f"   Richardson外推: {'启用' if self.enable_richardson_extrapolation else '禁用'}")
    
    def monte_carlo_analysis(self, base_parameters: Dict[str, float],
                           parameter_uncertainties: Dict[str, float],
                           simulation_function: Callable,
                           n_samples: Optional[int] = None) -> Dict[str, UncertaintyResult]:
        """
        Monte Carlo不确定性分析
        
        Args:
            base_parameters: 基准参数值
            parameter_uncertainties: 参数不确定性（相对值）
            simulation_function: 仿真函数
            n_samples: 样本数量
            
        Returns:
            不确定性分析结果
        """
        n_samples = n_samples or self.monte_carlo_samples
        
        self.logger.info(f"开始Monte Carlo分析，样本数: {n_samples}")
        
        # 生成参数样本
        parameter_samples = self._generate_parameter_samples(
            base_parameters, parameter_uncertainties, n_samples
        )
        
        # 运行仿真
        output_samples = {}
        
        for i, params in enumerate(parameter_samples):
            if i % (n_samples // 10) == 0:
                self.logger.info(f"Monte Carlo进度: {i/n_samples*100:.1f}%")
            
            try:
                result = simulation_function(params)
                
                # 收集输出
                for key, value in result.items():
                    if key not in output_samples:
                        output_samples[key] = []
                    output_samples[key].append(value)
                    
            except Exception as e:
                self.logger.warning(f"仿真样本 {i} 失败: {e}")
                continue
        
        # 分析不确定性
        uncertainty_results = {}
        for output_name, values in output_samples.items():
            values = np.array(values)
            uncertainty_results[output_name] = self._analyze_output_uncertainty(
                output_name, values
            )
        
        self.logger.info("Monte Carlo分析完成")
        return uncertainty_results
    
    def _generate_parameter_samples(self, base_parameters: Dict[str, float],
                                   uncertainties: Dict[str, float],
                                   n_samples: int) -> List[Dict[str, float]]:
        """生成参数样本"""
        samples = []
        
        for i in range(n_samples):
            sample = {}
            for param_name, base_value in base_parameters.items():
                if param_name in uncertainties:
                    # 假设正态分布
                    std_dev = base_value * uncertainties[param_name]
                    sample[param_name] = np.random.normal(base_value, std_dev)
                else:
                    sample[param_name] = base_value
            samples.append(sample)
        
        return samples
    
    def _analyze_output_uncertainty(self, output_name: str,
                                  values: np.ndarray) -> UncertaintyResult:
        """分析输出不确定性"""
        # 基本统计量
        mean_value = np.mean(values)
        std_dev = np.std(values)
        
        # 置信区间
        alpha = 1 - self.confidence_level
        confidence_interval = np.percentile(values, [alpha/2*100, (1-alpha/2)*100])
        
        # 不确定性百分比
        uncertainty_percentage = (std_dev / abs(mean_value)) * 100 if mean_value != 0 else 0
        
        # 分布类型检验
        distribution_type = self._identify_distribution(values)
        
        return UncertaintyResult(
            parameter_name=output_name,
            mean_value=mean_value,
            standard_deviation=std_dev,
            confidence_interval=confidence_interval,
            uncertainty_percentage=uncertainty_percentage,
            distribution_type=distribution_type,
            samples=values
        )
    
    def _identify_distribution(self, values: np.ndarray) -> str:
        """识别分布类型"""
        # 简化的分布识别
        # 正态性检验
        _, p_normal = stats.normaltest(values)
        
        if p_normal > 0.05:
            return "normal"
        else:
            # 检查偏度
            skewness = stats.skew(values)
            if abs(skewness) < 0.5:
                return "approximately_normal"
            elif skewness > 0:
                return "right_skewed"
            else:
                return "left_skewed"
    
    def richardson_extrapolation(self, solutions: List[float], 
                                refinement_ratio: float = 2.0) -> Dict[str, float]:
        """
        Richardson外推法网格收敛性分析
        
        Args:
            solutions: 不同网格级别的解（从粗到细）
            refinement_ratio: 网格细化比例
            
        Returns:
            外推分析结果
        """
        if not self.enable_richardson_extrapolation:
            return {'error': float('inf'), 'extrapolated_value': solutions[-1]}
        
        if len(solutions) < 3:
            warnings.warn("Richardson外推需要至少3个网格级别")
            return {'error': float('inf'), 'extrapolated_value': solutions[-1]}
        
        # 取最细的三个网格
        f1, f2, f3 = solutions[-3:]  # f1最粗，f3最细
        
        # 计算收敛阶
        if abs(f2 - f3) > 1e-12 and abs(f1 - f2) > 1e-12:
            p = np.log(abs((f1 - f2) / (f2 - f3))) / np.log(refinement_ratio)
            p = max(1.0, min(p, 4.0))  # 限制在合理范围
        else:
            return {'error': 0.0, 'extrapolated_value': f3, 'convergence_order': float('inf')}
        
        # Richardson外推值
        f_exact = f3 + (f3 - f2) / (refinement_ratio**p - 1)
        
        # 误差估计
        relative_error = abs(f3 - f_exact) / abs(f_exact) if f_exact != 0 else 0
        
        # Grid Convergence Index (GCI)
        safety_factor = 1.25 if len(solutions) >= 3 else 3.0
        gci = safety_factor * abs((f3 - f2) / f3) / (refinement_ratio**p - 1)
        
        return {
            'extrapolated_value': f_exact,
            'convergence_order': p,
            'relative_error': relative_error,
            'gci': gci,
            'error': relative_error
        }
    
    def sensitivity_analysis(self, base_parameters: Dict[str, float],
                           simulation_function: Callable,
                           perturbation_factor: float = 0.01) -> Dict[str, float]:
        """
        参数敏感性分析
        
        Args:
            base_parameters: 基准参数
            simulation_function: 仿真函数
            perturbation_factor: 扰动因子
            
        Returns:
            敏感性系数
        """
        base_result = simulation_function(base_parameters)
        base_output = list(base_result.values())[0]  # 假设只有一个输出
        
        sensitivities = {}
        
        for param_name, base_value in base_parameters.items():
            # 正向扰动
            perturbed_params = base_parameters.copy()
            perturbed_params[param_name] = base_value * (1 + perturbation_factor)
            
            try:
                perturbed_result = simulation_function(perturbed_params)
                perturbed_output = list(perturbed_result.values())[0]
                
                # 计算敏感性系数
                sensitivity = ((perturbed_output - base_output) / base_output) / perturbation_factor
                sensitivities[param_name] = sensitivity
                
            except Exception as e:
                self.logger.warning(f"参数 {param_name} 敏感性分析失败: {e}")
                sensitivities[param_name] = 0.0
        
        return sensitivities


class MonteCarloAnalyzer:
    """Monte Carlo分析器"""
    
    def __init__(self, config: Dict[str, Any]):
        """初始化Monte Carlo分析器"""
        self.n_samples = config.get('n_samples', 1000)
        self.confidence_level = config.get('confidence_level', 0.95)
        self.enable_parallel = config.get('enable_parallel', False)
        
        print(f"✅ Monte Carlo分析器初始化完成")
        print(f"   样本数: {self.n_samples}")
        print(f"   置信水平: {self.confidence_level}")
    
    def run_analysis(self, parameter_distributions: Dict[str, Dict],
                    simulation_function: Callable) -> Dict[str, Any]:
        """
        运行Monte Carlo分析
        
        Args:
            parameter_distributions: 参数分布定义
            simulation_function: 仿真函数
            
        Returns:
            分析结果
        """
        # 生成样本
        samples = self._generate_samples(parameter_distributions)
        
        # 运行仿真
        results = []
        for i, sample in enumerate(samples):
            if i % (self.n_samples // 10) == 0:
                print(f"Monte Carlo进度: {i/self.n_samples*100:.1f}%")
            
            try:
                result = simulation_function(sample)
                results.append(result)
            except Exception as e:
                print(f"样本 {i} 失败: {e}")
                continue
        
        # 统计分析
        return self._analyze_results(results)
    
    def _generate_samples(self, distributions: Dict[str, Dict]) -> List[Dict[str, float]]:
        """生成参数样本"""
        samples = []
        
        for i in range(self.n_samples):
            sample = {}
            for param_name, dist_config in distributions.items():
                dist_type = dist_config.get('type', 'normal')
                
                if dist_type == 'normal':
                    mean = dist_config.get('mean', 0.0)
                    std = dist_config.get('std', 1.0)
                    sample[param_name] = np.random.normal(mean, std)
                elif dist_type == 'uniform':
                    low = dist_config.get('min', 0.0)
                    high = dist_config.get('max', 1.0)
                    sample[param_name] = np.random.uniform(low, high)
                else:
                    raise ValueError(f"不支持的分布类型: {dist_type}")
            
            samples.append(sample)
        
        return samples
    
    def _analyze_results(self, results: List[Dict]) -> Dict[str, Any]:
        """分析结果"""
        if not results:
            return {'error': 'No valid results'}
        
        # 提取所有输出变量
        output_keys = results[0].keys()
        analysis = {}
        
        for key in output_keys:
            values = np.array([r[key] for r in results])
            
            analysis[key] = {
                'mean': np.mean(values),
                'std': np.std(values),
                'min': np.min(values),
                'max': np.max(values),
                'percentiles': {
                    '5%': np.percentile(values, 5),
                    '25%': np.percentile(values, 25),
                    '50%': np.percentile(values, 50),
                    '75%': np.percentile(values, 75),
                    '95%': np.percentile(values, 95)
                }
            }
        
        return analysis


class StatisticalValidator:
    """统计验证器"""

    def __init__(self, config: Dict[str, Any]):
        """初始化统计验证器"""
        self.significance_level = config.get('significance_level', 0.05)
        self.min_sample_size = config.get('min_sample_size', 30)

    def t_test_validation(self, computed_values: np.ndarray,
                         reference_values: np.ndarray) -> Dict[str, Any]:
        """t检验验证"""
        if len(computed_values) < self.min_sample_size:
            return {'warning': 'Sample size too small for reliable t-test'}

        # 双样本t检验
        t_stat, p_value = stats.ttest_ind(computed_values, reference_values)

        # 判断是否显著不同
        significantly_different = p_value < self.significance_level

        return {
            't_statistic': t_stat,
            'p_value': p_value,
            'significantly_different': significantly_different,
            'confidence_level': 1 - self.significance_level
        }

    def kolmogorov_smirnov_test(self, computed_values: np.ndarray,
                               reference_values: np.ndarray) -> Dict[str, Any]:
        """Kolmogorov-Smirnov检验"""
        ks_stat, p_value = stats.ks_2samp(computed_values, reference_values)

        significantly_different = p_value < self.significance_level

        return {
            'ks_statistic': ks_stat,
            'p_value': p_value,
            'significantly_different': significantly_different,
            'test_type': 'kolmogorov_smirnov'
        }
