"""
学术验证框架
============

基于advice_detailed.md要求，实现完整的学术验证功能：
- 集成不确定性量化
- 基准测试管理
- 自动化验证流程
- 学术报告生成
"""

import numpy as np
import warnings
from typing import Dict, Any, Optional, List, Callable
from pathlib import Path
import logging
import json
from datetime import datetime

from .uncertainty_quantification import UncertaintyQuantifier, MonteCarloAnalyzer, StatisticalValidator
from .benchmark_tests import BenchmarkTestSuite, create_standard_benchmark_suite


class AcademicValidationFramework:
    """学术验证框架"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化学术验证框架
        
        Args:
            config: 配置参数
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 初始化子模块
        self.uncertainty_quantifier = UncertaintyQuantifier(
            config.get('uncertainty_config', {})
        )
        self.monte_carlo_analyzer = MonteCarloAnalyzer(
            config.get('monte_carlo_config', {})
        )
        self.statistical_validator = StatisticalValidator(
            config.get('statistical_config', {})
        )
        self.benchmark_suite = create_standard_benchmark_suite(
            config.get('benchmark_config', {})
        )
        
        # 验证结果存储
        self.validation_results = {}
        self.uncertainty_results = {}
        self.benchmark_results = {}
        
        # 配置参数
        self.enable_uncertainty_quantification = config.get('enable_uncertainty_quantification', True)
        self.enable_benchmark_testing = config.get('enable_benchmark_testing', True)
        self.enable_statistical_validation = config.get('enable_statistical_validation', True)
        self.auto_generate_reports = config.get('auto_generate_reports', True)
        
        print(f"✅ 学术验证框架初始化完成")
        print(f"   不确定性量化: {'启用' if self.enable_uncertainty_quantification else '禁用'}")
        print(f"   基准测试: {'启用' if self.enable_benchmark_testing else '禁用'}")
        print(f"   统计验证: {'启用' if self.enable_statistical_validation else '禁用'}")
        print(f"   自动报告: {'启用' if self.auto_generate_reports else '禁用'}")
    
    def run_comprehensive_validation(self, solver_function: Callable,
                                   parameter_config: Dict[str, Any],
                                   validation_cases: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        运行综合验证分析
        
        Args:
            solver_function: 求解器函数
            parameter_config: 参数配置
            validation_cases: 指定的验证案例（可选）
            
        Returns:
            综合验证结果
        """
        self.logger.info("开始综合验证分析")
        
        comprehensive_results = {
            'timestamp': datetime.now().isoformat(),
            'configuration': parameter_config,
            'validation_summary': {}
        }
        
        # 1. 不确定性量化分析
        if self.enable_uncertainty_quantification:
            self.logger.info("执行不确定性量化分析")
            uncertainty_results = self._run_uncertainty_analysis(
                solver_function, parameter_config
            )
            comprehensive_results['uncertainty_analysis'] = uncertainty_results
            self.uncertainty_results = uncertainty_results
        
        # 2. 基准测试验证
        if self.enable_benchmark_testing:
            self.logger.info("执行基准测试验证")
            benchmark_results = self._run_benchmark_validation(
                solver_function, validation_cases
            )
            comprehensive_results['benchmark_validation'] = benchmark_results
            self.benchmark_results = benchmark_results
        
        # 3. 统计显著性验证
        if self.enable_statistical_validation:
            self.logger.info("执行统计显著性验证")
            statistical_results = self._run_statistical_validation()
            comprehensive_results['statistical_validation'] = statistical_results
        
        # 4. 生成验证汇总
        validation_summary = self._generate_validation_summary(comprehensive_results)
        comprehensive_results['validation_summary'] = validation_summary
        
        # 5. 自动生成报告
        if self.auto_generate_reports:
            self._generate_academic_report(comprehensive_results)
        
        self.validation_results = comprehensive_results
        
        self.logger.info("综合验证分析完成")
        return comprehensive_results
    
    def _run_uncertainty_analysis(self, solver_function: Callable,
                                parameter_config: Dict[str, Any]) -> Dict[str, Any]:
        """运行不确定性分析"""
        base_parameters = parameter_config.get('base_parameters', {})
        parameter_uncertainties = parameter_config.get('parameter_uncertainties', {})
        
        # Monte Carlo不确定性分析
        mc_results = self.uncertainty_quantifier.monte_carlo_analysis(
            base_parameters, parameter_uncertainties, solver_function
        )
        
        # 敏感性分析
        sensitivity_results = self.uncertainty_quantifier.sensitivity_analysis(
            base_parameters, solver_function
        )
        
        # Richardson外推（如果有多网格解）
        richardson_results = {}
        if 'grid_solutions' in parameter_config:
            grid_solutions = parameter_config['grid_solutions']
            for output_name, solutions in grid_solutions.items():
                richardson_results[output_name] = self.uncertainty_quantifier.richardson_extrapolation(
                    solutions
                )
        
        return {
            'monte_carlo': {name: {
                'mean': result.mean_value,
                'std': result.standard_deviation,
                'uncertainty_percentage': result.uncertainty_percentage,
                'confidence_interval': result.confidence_interval,
                'distribution_type': result.distribution_type
            } for name, result in mc_results.items()},
            'sensitivity': sensitivity_results,
            'richardson_extrapolation': richardson_results
        }
    
    def _run_benchmark_validation(self, solver_function: Callable,
                                validation_cases: Optional[List[str]] = None) -> Dict[str, Any]:
        """运行基准测试验证"""
        if validation_cases:
            # 只运行指定的测试案例
            selected_cases = {name: case for name, case in self.benchmark_suite.test_cases.items()
                            if name in validation_cases}
            temp_suite = BenchmarkTestSuite(self.config.get('benchmark_config', {}))
            temp_suite.test_cases = selected_cases
            results = temp_suite.run_validation_suite(solver_function)
        else:
            # 运行所有测试案例
            results = self.benchmark_suite.run_validation_suite(solver_function)
        
        return self.benchmark_suite.get_validation_summary()
    
    def _run_statistical_validation(self) -> Dict[str, Any]:
        """运行统计显著性验证"""
        statistical_results = {}
        
        # 如果有Monte Carlo结果和基准测试结果，进行统计比较
        if self.uncertainty_results and self.benchmark_results:
            for output_name in self.uncertainty_results.get('monte_carlo', {}):
                if output_name in self.benchmark_results.get('individual_results', {}):
                    # 获取Monte Carlo样本
                    mc_data = self.uncertainty_results['monte_carlo'][output_name]
                    
                    # 创建参考数据（基于基准测试）
                    benchmark_data = self.benchmark_results['individual_results'][output_name]
                    
                    # 这里需要实际的样本数据进行统计检验
                    # 简化实现：基于均值和标准差生成样本
                    if 'mean' in mc_data and 'std' in mc_data:
                        computed_samples = np.random.normal(
                            mc_data['mean'], mc_data['std'], 100
                        )
                        
                        # 假设参考值的变异性
                        if 'score' in benchmark_data:
                            reference_mean = mc_data['mean'] * benchmark_data['score']
                            reference_std = mc_data['std'] * 0.1  # 假设参考数据变异性较小
                            reference_samples = np.random.normal(reference_mean, reference_std, 100)
                            
                            # 执行统计检验
                            t_test_result = self.statistical_validator.t_test_validation(
                                computed_samples, reference_samples
                            )
                            ks_test_result = self.statistical_validator.kolmogorov_smirnov_test(
                                computed_samples, reference_samples
                            )
                            
                            statistical_results[output_name] = {
                                't_test': t_test_result,
                                'ks_test': ks_test_result
                            }
        
        return statistical_results
    
    def _generate_validation_summary(self, comprehensive_results: Dict[str, Any]) -> Dict[str, Any]:
        """生成验证汇总"""
        summary = {
            'overall_validation_score': 0.0,
            'uncertainty_assessment': 'unknown',
            'benchmark_performance': 'unknown',
            'statistical_significance': 'unknown',
            'recommendations': []
        }
        
        # 基准测试分数
        if 'benchmark_validation' in comprehensive_results:
            benchmark_score = comprehensive_results['benchmark_validation'].get('average_score', 0.0)
            summary['overall_validation_score'] += benchmark_score * 0.5
            
            if benchmark_score > 0.8:
                summary['benchmark_performance'] = 'excellent'
            elif benchmark_score > 0.6:
                summary['benchmark_performance'] = 'good'
            elif benchmark_score > 0.4:
                summary['benchmark_performance'] = 'acceptable'
            else:
                summary['benchmark_performance'] = 'poor'
                summary['recommendations'].append('改进求解器精度以通过更多基准测试')
        
        # 不确定性评估
        if 'uncertainty_analysis' in comprehensive_results:
            mc_results = comprehensive_results['uncertainty_analysis'].get('monte_carlo', {})
            avg_uncertainty = np.mean([
                result.get('uncertainty_percentage', 0) 
                for result in mc_results.values()
            ]) if mc_results else 0
            
            summary['overall_validation_score'] += (1.0 - min(avg_uncertainty/100, 1.0)) * 0.3
            
            if avg_uncertainty < 5:
                summary['uncertainty_assessment'] = 'low'
            elif avg_uncertainty < 15:
                summary['uncertainty_assessment'] = 'moderate'
            else:
                summary['uncertainty_assessment'] = 'high'
                summary['recommendations'].append('考虑改进模型以降低不确定性')
        
        # 统计显著性
        if 'statistical_validation' in comprehensive_results:
            stat_results = comprehensive_results['statistical_validation']
            significant_differences = sum(
                1 for result in stat_results.values()
                if result.get('t_test', {}).get('significantly_different', False)
            )
            
            if significant_differences == 0:
                summary['statistical_significance'] = 'not_significant'
                summary['overall_validation_score'] += 0.2
            else:
                summary['statistical_significance'] = 'significant'
                summary['recommendations'].append('检查显著差异的原因')
        
        # 限制分数在0-1范围内
        summary['overall_validation_score'] = min(summary['overall_validation_score'], 1.0)
        
        return summary
    
    def _generate_academic_report(self, comprehensive_results: Dict[str, Any]) -> None:
        """生成学术验证报告"""
        report_dir = Path("validation_reports")
        report_dir.mkdir(exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = report_dir / f"academic_validation_report_{timestamp}.json"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(comprehensive_results, f, indent=2, ensure_ascii=False, default=str)
        
        self.logger.info(f"学术验证报告已生成: {report_file}")
    
    def get_validation_status(self) -> Dict[str, Any]:
        """获取验证状态"""
        if not self.validation_results:
            return {'status': 'not_validated'}
        
        summary = self.validation_results.get('validation_summary', {})
        overall_score = summary.get('overall_validation_score', 0.0)
        
        if overall_score > 0.8:
            status = 'excellent'
        elif overall_score > 0.6:
            status = 'good'
        elif overall_score > 0.4:
            status = 'acceptable'
        else:
            status = 'needs_improvement'
        
        return {
            'status': status,
            'overall_score': overall_score,
            'benchmark_performance': summary.get('benchmark_performance', 'unknown'),
            'uncertainty_assessment': summary.get('uncertainty_assessment', 'unknown'),
            'recommendations': summary.get('recommendations', [])
        }
