"""
升力线求解器实现
==============

升力线理论求解器 - 中等保真度气动计算
基于Prandtl升力线理论，适用于中等保真度快速计算
"""

import numpy as np
import time
from typing import Dict, Any, Tuple, Optional
from ..base import AerodynamicSolverBase
from ...interfaces.solver_interface import SolverConfig, FidelityLevel

class LiftingLineSolver(AerodynamicSolverBase):
    """升力线理论求解器 - 重构版本"""
    
    def __init__(self, config: SolverConfig):
        super().__init__(config)
        
        # 升力线特定参数
        self.control_points_count = config.get('n_elements', config.get('control_points_count', 15))
        self.fourier_modes = config.get('fourier_modes', 10)
        self.enable_tip_correction = config.get('enable_tip_correction', True)
        self.enable_root_correction = config.get('enable_root_correction', True)

        # 数值参数
        self.relaxation_factor = config.get('relaxation_factor', 0.3)
        self.max_iterations = config.get('max_iterations', 100)
        self.tolerance = config.get('convergence_tolerance', 1e-6)
        
        # 升力线数据
        self.control_points = None
        self.circulation_distribution = None
        self.downwash_distribution = None
        self.convergence_history = []
        
        print(f"✅ 升力线求解器初始化完成")
        print(f"   控制点数: {self.control_points_count}")
        print(f"   傅里叶模态数: {self.fourier_modes}")
    
    @property
    def solver_name(self) -> str:
        return "LiftingLine"

    def initialize(self, geometry_data: Dict[str, Any]) -> None:
        """初始化升力线求解器几何"""
        self._setup_blade_elements(geometry_data)
        self._is_initialized = True
        print(f"升力线求解器几何初始化完成")
        print(f"   控制点数: {self.control_points_count}")

    def initialize_solver(self, geometry_data: Dict[str, Any]) -> None:
        """初始化求解器几何 - 别名方法，用于兼容性"""
        self.initialize(geometry_data)

    def solve_timestep(self, boundary_conditions: Dict[str, Any], dt: float) -> Dict[str, Any]:
        """求解单个时间步"""
        if not hasattr(self, '_is_initialized') or not self._is_initialized:
            raise RuntimeError("升力线求解器未初始化")

        # 提取边界条件
        rotor_rpm = boundary_conditions.get('rotor_rpm', 1500.0)
        omega = rotor_rpm * 2 * np.pi / 60.0  # 转换为rad/s

        # 简化的升力线求解（基于Fourier级数展开）
        circulation = self._solve_lifting_line_simplified(omega)

        # 计算力和力矩（简化版本）
        thrust = np.sum(circulation) * 0.08  # 简化的推力计算
        torque = np.sum(circulation) * 0.04  # 简化的扭矩计算
        power = torque * omega

        return {
            'thrust': thrust,
            'torque': torque,
            'power': power,
            'circulation': circulation,
            'converged': True
        }

    def _solve_lifting_line_simplified(self, omega: float) -> np.ndarray:
        """完整的升力线求解（增强版）"""
        # 获取控制点
        r_stations = self.blade_elements['r_stations']
        n_points = len(r_stations)

        # 初始化环量分布
        circulation = np.zeros(n_points)

        # 迭代求解
        for iteration in range(self.max_iterations):
            circulation_old = circulation.copy()

            # 计算诱导速度矩阵
            influence_matrix = self._build_influence_matrix(r_stations)

            # 计算右端项（基于边界条件）
            rhs_vector = self._build_rhs_vector(r_stations, omega)

            # 应用Kutta条件
            influence_matrix, rhs_vector = self._apply_kutta_condition(
                influence_matrix, rhs_vector
            )

            # 求解线性系统
            try:
                circulation = np.linalg.solve(influence_matrix, rhs_vector)
            except np.linalg.LinAlgError:
                print("   ⚠️ 升力线求解器矩阵奇异，使用最小二乘解")
                circulation = np.linalg.lstsq(influence_matrix, rhs_vector, rcond=None)[0]

            # 应用松弛因子
            circulation = (1 - self.relaxation_factor) * circulation_old + \
                         self.relaxation_factor * circulation

            # 检查收敛性
            residual = np.linalg.norm(circulation - circulation_old)
            self.convergence_history.append(residual)

            if residual < self.tolerance:
                print(f"   ✅ 升力线求解收敛，迭代次数: {iteration + 1}")
                break
        else:
            print(f"   ⚠️ 升力线求解未收敛，最大迭代次数: {self.max_iterations}")

        return circulation

    def _build_influence_matrix(self, r_stations: np.ndarray) -> np.ndarray:
        """构建影响系数矩阵"""
        n_points = len(r_stations)
        influence_matrix = np.zeros((n_points, n_points))

        for i in range(n_points):
            for j in range(n_points):
                if i != j:
                    # 计算涡丝诱导速度
                    r_i = r_stations[i]
                    r_j = r_stations[j]

                    # 简化的诱导速度计算（基于Biot-Savart定律）
                    distance = abs(r_i - r_j)
                    if distance > 1e-6:
                        influence_matrix[i, j] = 1.0 / (4 * np.pi * distance)
                    else:
                        influence_matrix[i, j] = 0.0
                else:
                    # 自诱导项（奇点处理）
                    influence_matrix[i, i] = 0.0

        return influence_matrix

    def _build_rhs_vector(self, r_stations: np.ndarray, omega: float) -> np.ndarray:
        """构建右端项向量"""
        n_points = len(r_stations)
        rhs_vector = np.zeros(n_points)

        # 基于边界条件构建右端项
        for i, r in enumerate(r_stations):
            # 简化的边界条件：无穿透条件
            # 这里应该基于翼型几何和攻角分布
            local_velocity = omega * r
            rhs_vector[i] = local_velocity * 0.1  # 简化计算

        return rhs_vector

    def _apply_kutta_condition(self, influence_matrix: np.ndarray,
                             rhs_vector: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """应用Kutta条件"""
        # 在翼尖处应用Kutta条件：环量为零
        n_points = len(rhs_vector)

        # 修改最后一行以强制翼尖环量为零
        influence_matrix[-1, :] = 0.0
        influence_matrix[-1, -1] = 1.0
        rhs_vector[-1] = 0.0

        # 如果启用根部修正，也在根部应用条件
        if self.enable_root_correction:
            influence_matrix[0, :] = 0.0
            influence_matrix[0, 0] = 1.0
            rhs_vector[0] = 0.0

        return influence_matrix, rhs_vector

    def solve_lifting_line_fourier(self, boundary_conditions: Dict[str, Any]) -> Dict[str, Any]:
        """使用Fourier级数展开的升力线求解（完整实现）"""
        # 提取边界条件
        rotor_rpm = boundary_conditions.get('rotor_rpm', 1500.0)
        omega = rotor_rpm * 2 * np.pi / 60.0

        # 获取几何参数
        r_stations = self.blade_elements['r_stations']
        chord_distribution = self.blade_elements.get('chord_distribution',
                                                   np.ones(len(r_stations)) * 0.1)
        twist_distribution = self.blade_elements.get('twist_distribution',
                                                   np.zeros(len(r_stations)))

        # 设置Fourier级数展开
        theta_stations = np.arccos(-2 * (r_stations - r_stations[0]) /
                                  (r_stations[-1] - r_stations[0]) + 1)

        # 初始化Fourier系数
        fourier_coeffs = np.zeros(self.fourier_modes)

        # 迭代求解Fourier系数
        for iteration in range(self.max_iterations):
            fourier_coeffs_old = fourier_coeffs.copy()

            # 构建Fourier系统
            fourier_matrix, fourier_rhs = self._build_fourier_system(
                theta_stations, chord_distribution, twist_distribution, omega
            )

            # 求解Fourier系数
            try:
                fourier_coeffs = np.linalg.solve(fourier_matrix, fourier_rhs)
            except np.linalg.LinAlgError:
                fourier_coeffs = np.linalg.lstsq(fourier_matrix, fourier_rhs, rcond=None)[0]

            # 应用松弛因子
            fourier_coeffs = (1 - self.relaxation_factor) * fourier_coeffs_old + \
                            self.relaxation_factor * fourier_coeffs

            # 检查收敛性
            residual = np.linalg.norm(fourier_coeffs - fourier_coeffs_old)
            if residual < self.tolerance:
                break

        # 从Fourier系数重构环量分布
        circulation = self._reconstruct_circulation_from_fourier(
            fourier_coeffs, theta_stations, r_stations
        )

        # 计算气动载荷
        forces_moments = self._calculate_forces_moments_fourier(
            circulation, r_stations, chord_distribution, omega
        )

        return {
            'circulation': circulation,
            'fourier_coefficients': fourier_coeffs,
            'theta_stations': theta_stations,
            'convergence_history': self.convergence_history,
            **forces_moments
        }

    def _build_fourier_system(self, theta_stations: np.ndarray,
                            chord_distribution: np.ndarray,
                            twist_distribution: np.ndarray,
                            omega: float) -> Tuple[np.ndarray, np.ndarray]:
        """构建Fourier级数系统"""
        n_stations = len(theta_stations)
        n_modes = self.fourier_modes

        # 构建系数矩阵
        fourier_matrix = np.zeros((n_modes, n_modes))
        fourier_rhs = np.zeros(n_modes)

        for i in range(n_modes):
            for j in range(n_modes):
                # 计算Fourier系数的影响矩阵
                mode_i = i + 1
                mode_j = j + 1

                # 简化的Fourier影响系数
                if i == j:
                    fourier_matrix[i, j] = mode_i * np.pi / 2
                else:
                    fourier_matrix[i, j] = 0.0

            # 构建右端项
            mode_i = i + 1
            fourier_rhs[i] = self._calculate_fourier_rhs_term(
                mode_i, theta_stations, chord_distribution, twist_distribution, omega
            )

        return fourier_matrix, fourier_rhs

    def _calculate_fourier_rhs_term(self, mode: int, theta_stations: np.ndarray,
                                  chord_distribution: np.ndarray,
                                  twist_distribution: np.ndarray,
                                  omega: float) -> float:
        """计算Fourier右端项"""
        # 简化的右端项计算
        integrand = np.sin(mode * theta_stations) * chord_distribution * omega

        # 数值积分（梯形法则）
        rhs_term = np.trapz(integrand, theta_stations)

        return rhs_term

    def _reconstruct_circulation_from_fourier(self, fourier_coeffs: np.ndarray,
                                            theta_stations: np.ndarray,
                                            r_stations: np.ndarray) -> np.ndarray:
        """从Fourier系数重构环量分布"""
        circulation = np.zeros(len(r_stations))

        for i, theta in enumerate(theta_stations):
            for n, coeff in enumerate(fourier_coeffs):
                mode = n + 1
                circulation[i] += coeff * np.sin(mode * theta)

        return circulation

    def _calculate_forces_moments_fourier(self, circulation: np.ndarray,
                                        r_stations: np.ndarray,
                                        chord_distribution: np.ndarray,
                                        omega: float) -> Dict[str, float]:
        """基于Fourier解计算力和力矩"""
        # 简化的力和力矩计算
        dr = np.diff(r_stations)
        dr = np.append(dr, dr[-1])  # 扩展到相同长度

        # 推力计算
        thrust_distribution = circulation * omega * 1.225  # 简化，假设密度1.225 kg/m³
        thrust = np.sum(thrust_distribution * dr)

        # 扭矩计算
        torque_distribution = circulation * r_stations * omega * 0.1  # 简化
        torque = np.sum(torque_distribution * dr)

        # 功率
        power = torque * omega

        return {
            'thrust': thrust,
            'torque': torque,
            'power': power,
            'thrust_distribution': thrust_distribution,
            'torque_distribution': torque_distribution
        }

    def _setup_blade_elements(self, geometry_data: Dict[str, Any]) -> None:
        """设置升力线控制点"""
        rotor_radius = geometry_data.get('rotor_radius', 1.0)
        blade_count = geometry_data.get('blade_count', 3)
        hub_radius = geometry_data.get('hub_radius', 0.1 * rotor_radius)
        
        # 创建控制点分布（余弦分布以改善收敛性）
        theta = np.linspace(0, np.pi, self.control_points_count + 2)[1:-1]  # 排除端点
        eta = -np.cos(theta)  # 余弦分布 [-1, 1]
        
        # 映射到径向位置
        r_stations = hub_radius + (rotor_radius - hub_radius) * (eta + 1) / 2
        
        # 获取几何分布
        chord_input = geometry_data.get('chord_distribution', np.ones(10) * 0.1)
        twist_input = geometry_data.get('twist_distribution', np.linspace(-8, -18, 10) * np.pi/180)

        # 插值到控制点分布
        if len(chord_input) != self.control_points_count:
            r_input = np.linspace(hub_radius, rotor_radius, len(chord_input))
            chord_distribution = np.interp(r_stations, r_input, chord_input)
        else:
            chord_distribution = chord_input

        if len(twist_input) != self.control_points_count:
            r_input = np.linspace(hub_radius, rotor_radius, len(twist_input))
            twist_distribution = np.interp(r_stations, r_input, twist_input)
        else:
            twist_distribution = twist_input
        
        self.blade_elements = {
            'r_stations': r_stations,
            'eta_stations': eta,  # 无量纲径向位置
            'chord': chord_distribution,
            'twist': twist_distribution,
            'blade_count': blade_count,
            'rotor_radius': rotor_radius,
            'hub_radius': hub_radius
        }
        
        # 初始化环量分布
        self.circulation_distribution = np.zeros(self.control_points_count)
        self.downwash_distribution = np.zeros(self.control_points_count)
    
    def _initialize_wake(self) -> None:
        """初始化尾迹（升力线中简化为诱导因子）"""
        self.wake_data = {
            'type': 'lifting_line',
            'circulation_distribution': self.circulation_distribution,
            'downwash_distribution': self.downwash_distribution,
            'convergence_history': []
        }
    
    def _compute_induced_velocity(self, blade_positions: np.ndarray) -> np.ndarray:
        """计算诱导速度"""
        induced_velocity = np.zeros_like(blade_positions)
        
        # 基于当前环量分布计算诱导速度
        for i, pos in enumerate(blade_positions):
            r = np.linalg.norm(pos[:2])  # 径向距离
            
            # 插值获取该位置的下洗速度
            downwash = np.interp(r, self.blade_elements['r_stations'], 
                               self.downwash_distribution)
            
            # 升力线理论中主要是轴向诱导速度
            induced_velocity[i, 2] = -downwash  # 负号表示向下
        
        return induced_velocity
    
    def _compute_blade_loads(self, velocity_field: np.ndarray, 
                           blade_positions: np.ndarray) -> tuple:
        """计算叶片载荷"""
        # 迭代求解环量分布
        circulation_new = self._solve_lifting_line_equation(velocity_field)
        
        # 计算气动力
        total_force, total_moment = self._compute_forces_from_circulation(
            circulation_new, velocity_field
        )
        
        # 更新环量分布（带松弛）
        self.circulation_distribution = (
            self.relaxation_factor * circulation_new + 
            (1 - self.relaxation_factor) * self.circulation_distribution
        )
        
        return total_force, total_moment
    
    def _solve_lifting_line_equation(self, velocity_field: np.ndarray) -> np.ndarray:
        """求解升力线方程"""
        r_stations = self.blade_elements['r_stations']
        chord = self.blade_elements['chord']
        twist = self.blade_elements['twist']
        eta_stations = self.blade_elements['eta_stations']
        
        # 设置升力线方程系统
        A_matrix = np.zeros((self.control_points_count, self.fourier_modes))
        rhs_vector = np.zeros(self.control_points_count)
        
        for i in range(self.control_points_count):
            eta_i = eta_stations[i]
            theta_i = np.arccos(-eta_i)
            
            # 计算局部攻角
            if i < len(velocity_field):
                local_velocity = velocity_field[i]
                V_mag = np.linalg.norm(local_velocity)
                
                if V_mag > 1e-6:
                    # 简化的攻角计算
                    alpha_local = twist[i] + np.arctan2(local_velocity[2], 
                                                       np.linalg.norm(local_velocity[:2]))
                else:
                    alpha_local = twist[i]
            else:
                alpha_local = twist[i]
            
            # 升力线系数矩阵
            for n in range(1, self.fourier_modes + 1):
                A_matrix[i, n-1] = (
                    np.sin(n * theta_i) * 
                    (1 + (n * chord[i]) / (4 * r_stations[i]) * 
                     self._get_lift_curve_slope())
                )
            
            # 右端项
            rhs_vector[i] = alpha_local * chord[i] / (4 * r_stations[i]) * self._get_lift_curve_slope()
        
        # 求解傅里叶系数
        try:
            fourier_coeffs = np.linalg.solve(A_matrix, rhs_vector)
        except np.linalg.LinAlgError:
            fourier_coeffs = np.linalg.lstsq(A_matrix, rhs_vector, rcond=None)[0]
        
        # 重构环量分布
        circulation = np.zeros(self.control_points_count)
        for i in range(self.control_points_count):
            eta_i = eta_stations[i]
            theta_i = np.arccos(-eta_i)
            
            for n in range(1, self.fourier_modes + 1):
                circulation[i] += fourier_coeffs[n-1] * np.sin(n * theta_i)
            
            # 应用修正
            if self.enable_tip_correction:
                circulation[i] *= self._tip_correction_factor(r_stations[i])
            
            if self.enable_root_correction:
                circulation[i] *= self._root_correction_factor(r_stations[i])
        
        # 更新下洗分布
        self._update_downwash_distribution(fourier_coeffs)
        
        return circulation
    
    def _get_lift_curve_slope(self) -> float:
        """获取升力线斜率"""
        return 2 * np.pi  # 理论值
    
    def _tip_correction_factor(self, r: float) -> float:
        """叶尖修正因子"""
        R = self.blade_elements['rotor_radius']
        B = self.blade_elements['blade_count']
        
        if r >= R:
            return 0.0
        
        # Prandtl叶尖损失修正
        f = B * (R - r) / (2 * r)
        F = (2 / np.pi) * np.arccos(np.exp(-f))
        
        return max(F, 0.1)
    
    def _root_correction_factor(self, r: float) -> float:
        """叶根修正因子"""
        R_hub = self.blade_elements['hub_radius']
        B = self.blade_elements['blade_count']
        
        if r <= R_hub:
            return 0.0
        
        # 叶根损失修正
        f_hub = B * (r - R_hub) / (2 * R_hub)
        F_hub = (2 / np.pi) * np.arccos(np.exp(-f_hub))
        
        return max(F_hub, 0.1)
    
    def _update_downwash_distribution(self, fourier_coeffs: np.ndarray) -> None:
        """更新下洗分布"""
        eta_stations = self.blade_elements['eta_stations']
        
        for i in range(self.control_points_count):
            eta_i = eta_stations[i]
            theta_i = np.arccos(-eta_i)
            
            downwash = 0.0
            for n in range(1, self.fourier_modes + 1):
                downwash += n * fourier_coeffs[n-1] * np.sin(n * theta_i) / np.sin(theta_i)
            
            self.downwash_distribution[i] = downwash
    
    def _compute_forces_from_circulation(self, circulation: np.ndarray,
                                       velocity_field: np.ndarray) -> tuple:
        """从环量分布计算气动力"""
        r_stations = self.blade_elements['r_stations']
        chord = self.blade_elements['chord']
        rho = 1.225  # 空气密度
        
        total_force = np.zeros(3)
        total_moment = np.zeros(3)
        
        for i in range(self.control_points_count):
            if i < len(velocity_field):
                local_velocity = velocity_field[i]
                V_mag = np.linalg.norm(local_velocity)
            else:
                V_mag = 10.0  # 默认速度
            
            # Kutta-Joukowski定理
            dr = (self.blade_elements['rotor_radius'] - self.blade_elements['hub_radius']) / self.control_points_count
            dL = rho * V_mag * circulation[i] * dr
            
            # 简化的力分量
            force_local = np.array([0, 0, dL])  # 主要是升力
            moment_local = np.array([0, 0, dL * r_stations[i]])  # 扭矩
            
            total_force += force_local
            total_moment += moment_local
        
        return total_force, total_moment
    
    def _update_wake(self, blade_positions: np.ndarray, velocity: np.ndarray) -> None:
        """更新尾迹（升力线中更新环量历史）"""
        self.wake_data['circulation_distribution'] = self.circulation_distribution.copy()
        self.wake_data['downwash_distribution'] = self.downwash_distribution.copy()
        
        # 记录收敛历史
        residual = np.linalg.norm(self.circulation_distribution - 
                                self.wake_data.get('prev_circulation', self.circulation_distribution))
        self.convergence_history.append(residual)
        self.wake_data['convergence_history'] = self.convergence_history
        
        self._iteration_count += 1
