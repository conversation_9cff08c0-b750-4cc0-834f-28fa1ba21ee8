"""
验证报告生成器
==============

基于advice_detailed.md要求，实现自动化验证报告生成功能：
- 学术格式报告
- 图表生成
- 统计分析可视化
- 多格式输出
"""

import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, Any, Optional, List
from pathlib import Path
import json
from datetime import datetime
import logging


class ValidationReportGenerator:
    """验证报告生成器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化验证报告生成器
        
        Args:
            config: 配置参数
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 报告配置
        self.output_dir = Path(config.get('output_dir', 'validation_reports'))
        self.output_dir.mkdir(exist_ok=True)
        
        self.enable_plots = config.get('enable_plots', True)
        self.plot_format = config.get('plot_format', 'png')
        self.plot_dpi = config.get('plot_dpi', 300)
        
        # 设置绘图样式
        if self.enable_plots:
            plt.style.use('seaborn-v0_8')
            sns.set_palette("husl")
        
        print(f"✅ 验证报告生成器初始化完成")
        print(f"   输出目录: {self.output_dir}")
        print(f"   图表生成: {'启用' if self.enable_plots else '禁用'}")
    
    def generate_comprehensive_report(self, validation_results: Dict[str, Any],
                                    report_title: str = "循环翼转子仿真验证报告") -> str:
        """
        生成综合验证报告
        
        Args:
            validation_results: 验证结果
            report_title: 报告标题
            
        Returns:
            报告文件路径
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_name = f"validation_report_{timestamp}"
        
        # 创建报告目录
        report_dir = self.output_dir / report_name
        report_dir.mkdir(exist_ok=True)
        
        self.logger.info(f"生成综合验证报告: {report_name}")
        
        # 生成各部分报告
        self._generate_executive_summary(validation_results, report_dir)
        self._generate_uncertainty_analysis_report(validation_results, report_dir)
        self._generate_benchmark_validation_report(validation_results, report_dir)
        self._generate_statistical_analysis_report(validation_results, report_dir)
        
        # 生成图表
        if self.enable_plots:
            self._generate_validation_plots(validation_results, report_dir)
        
        # 生成主报告文件
        main_report_path = self._generate_main_report(
            validation_results, report_dir, report_title
        )
        
        self.logger.info(f"验证报告生成完成: {main_report_path}")
        return str(main_report_path)
    
    def _generate_executive_summary(self, validation_results: Dict[str, Any],
                                  report_dir: Path) -> None:
        """生成执行摘要"""
        summary_file = report_dir / "executive_summary.md"
        
        summary = validation_results.get('validation_summary', {})
        overall_score = summary.get('overall_validation_score', 0.0)
        
        with open(summary_file, 'w', encoding='utf-8') as f:
            f.write("# 执行摘要\n\n")
            f.write(f"**验证时间**: {validation_results.get('timestamp', 'Unknown')}\n\n")
            f.write(f"**总体验证分数**: {overall_score:.3f}/1.000\n\n")
            
            # 验证状态
            if overall_score > 0.8:
                status = "优秀 ✅"
                color = "green"
            elif overall_score > 0.6:
                status = "良好 ✅"
                color = "blue"
            elif overall_score > 0.4:
                status = "可接受 ⚠️"
                color = "orange"
            else:
                status = "需要改进 ❌"
                color = "red"
            
            f.write(f"**验证状态**: {status}\n\n")
            
            # 关键发现
            f.write("## 关键发现\n\n")
            
            # 基准测试结果
            if 'benchmark_validation' in validation_results:
                benchmark = validation_results['benchmark_validation']
                pass_rate = benchmark.get('pass_rate', 0.0)
                f.write(f"- **基准测试通过率**: {pass_rate*100:.1f}%\n")
            
            # 不确定性评估
            if 'uncertainty_analysis' in validation_results:
                uncertainty = validation_results['uncertainty_analysis']
                mc_results = uncertainty.get('monte_carlo', {})
                if mc_results:
                    avg_uncertainty = np.mean([
                        result.get('uncertainty_percentage', 0)
                        for result in mc_results.values()
                    ])
                    f.write(f"- **平均不确定性**: {avg_uncertainty:.1f}%\n")
            
            # 建议
            recommendations = summary.get('recommendations', [])
            if recommendations:
                f.write("\n## 建议\n\n")
                for i, rec in enumerate(recommendations, 1):
                    f.write(f"{i}. {rec}\n")
    
    def _generate_uncertainty_analysis_report(self, validation_results: Dict[str, Any],
                                            report_dir: Path) -> None:
        """生成不确定性分析报告"""
        if 'uncertainty_analysis' not in validation_results:
            return
        
        uncertainty_file = report_dir / "uncertainty_analysis.md"
        uncertainty_data = validation_results['uncertainty_analysis']
        
        with open(uncertainty_file, 'w', encoding='utf-8') as f:
            f.write("# 不确定性分析报告\n\n")
            
            # Monte Carlo分析
            if 'monte_carlo' in uncertainty_data:
                f.write("## Monte Carlo不确定性分析\n\n")
                mc_results = uncertainty_data['monte_carlo']
                
                f.write("| 输出参数 | 均值 | 标准差 | 不确定性(%) | 置信区间 | 分布类型 |\n")
                f.write("|---------|------|--------|-------------|----------|----------|\n")
                
                for param_name, result in mc_results.items():
                    mean = result.get('mean', 0.0)
                    std = result.get('std', 0.0)
                    uncertainty = result.get('uncertainty_percentage', 0.0)
                    ci = result.get('confidence_interval', [0, 0])
                    dist_type = result.get('distribution_type', 'unknown')
                    
                    f.write(f"| {param_name} | {mean:.4f} | {std:.4f} | {uncertainty:.2f}% | "
                           f"[{ci[0]:.4f}, {ci[1]:.4f}] | {dist_type} |\n")
            
            # 敏感性分析
            if 'sensitivity' in uncertainty_data:
                f.write("\n## 敏感性分析\n\n")
                sensitivity = uncertainty_data['sensitivity']
                
                f.write("| 输入参数 | 敏感性系数 | 影响程度 |\n")
                f.write("|---------|------------|----------|\n")
                
                for param_name, sensitivity_coeff in sensitivity.items():
                    abs_sensitivity = abs(sensitivity_coeff)
                    if abs_sensitivity > 1.0:
                        impact = "高"
                    elif abs_sensitivity > 0.1:
                        impact = "中"
                    else:
                        impact = "低"
                    
                    f.write(f"| {param_name} | {sensitivity_coeff:.4f} | {impact} |\n")
            
            # Richardson外推
            if 'richardson_extrapolation' in uncertainty_data:
                f.write("\n## Richardson外推分析\n\n")
                richardson = uncertainty_data['richardson_extrapolation']
                
                for param_name, result in richardson.items():
                    f.write(f"### {param_name}\n\n")
                    f.write(f"- **外推值**: {result.get('extrapolated_value', 0.0):.6f}\n")
                    f.write(f"- **收敛阶**: {result.get('convergence_order', 0.0):.2f}\n")
                    f.write(f"- **相对误差**: {result.get('relative_error', 0.0)*100:.3f}%\n")
                    f.write(f"- **GCI**: {result.get('gci', 0.0)*100:.3f}%\n\n")
    
    def _generate_benchmark_validation_report(self, validation_results: Dict[str, Any],
                                            report_dir: Path) -> None:
        """生成基准测试验证报告"""
        if 'benchmark_validation' not in validation_results:
            return
        
        benchmark_file = report_dir / "benchmark_validation.md"
        benchmark_data = validation_results['benchmark_validation']
        
        with open(benchmark_file, 'w', encoding='utf-8') as f:
            f.write("# 基准测试验证报告\n\n")
            
            # 总体统计
            total_tests = benchmark_data.get('total_tests', 0)
            passed_tests = benchmark_data.get('passed_tests', 0)
            pass_rate = benchmark_data.get('pass_rate', 0.0)
            avg_score = benchmark_data.get('average_score', 0.0)
            
            f.write("## 总体统计\n\n")
            f.write(f"- **总测试数**: {total_tests}\n")
            f.write(f"- **通过测试**: {passed_tests}\n")
            f.write(f"- **通过率**: {pass_rate*100:.1f}%\n")
            f.write(f"- **平均分数**: {avg_score:.3f}\n\n")
            
            # 详细结果
            if 'individual_results' in benchmark_data:
                f.write("## 详细测试结果\n\n")
                f.write("| 测试案例 | 状态 | 分数 | 主要误差 |\n")
                f.write("|---------|------|------|----------|\n")
                
                individual_results = benchmark_data['individual_results']
                for case_name, result in individual_results.items():
                    passed = result.get('passed', False)
                    score = result.get('score', 0.0)
                    status = "✅ 通过" if passed else "❌ 失败"
                    
                    # 找到最大相对误差
                    relative_errors = result.get('relative_errors', {})
                    if relative_errors:
                        max_error_param = max(relative_errors.keys(), 
                                            key=lambda k: relative_errors[k])
                        max_error = relative_errors[max_error_param]
                        error_info = f"{max_error_param}: {max_error*100:.1f}%"
                    else:
                        error_info = "N/A"
                    
                    f.write(f"| {case_name} | {status} | {score:.3f} | {error_info} |\n")
    
    def _generate_statistical_analysis_report(self, validation_results: Dict[str, Any],
                                            report_dir: Path) -> None:
        """生成统计分析报告"""
        if 'statistical_validation' not in validation_results:
            return
        
        statistical_file = report_dir / "statistical_analysis.md"
        statistical_data = validation_results['statistical_validation']
        
        with open(statistical_file, 'w', encoding='utf-8') as f:
            f.write("# 统计分析报告\n\n")
            
            if not statistical_data:
                f.write("无统计验证数据。\n")
                return
            
            f.write("## 统计显著性检验\n\n")
            f.write("| 参数 | t检验p值 | KS检验p值 | 显著差异 |\n")
            f.write("|------|----------|-----------|----------|\n")
            
            for param_name, tests in statistical_data.items():
                t_test = tests.get('t_test', {})
                ks_test = tests.get('ks_test', {})
                
                t_p_value = t_test.get('p_value', 1.0)
                ks_p_value = ks_test.get('p_value', 1.0)
                
                t_significant = t_test.get('significantly_different', False)
                ks_significant = ks_test.get('significantly_different', False)
                
                significance = "是" if (t_significant or ks_significant) else "否"
                
                f.write(f"| {param_name} | {t_p_value:.4f} | {ks_p_value:.4f} | {significance} |\n")
    
    def _generate_validation_plots(self, validation_results: Dict[str, Any],
                                 report_dir: Path) -> None:
        """生成验证图表"""
        plots_dir = report_dir / "plots"
        plots_dir.mkdir(exist_ok=True)
        
        # 1. 基准测试结果图
        if 'benchmark_validation' in validation_results:
            self._plot_benchmark_results(validation_results['benchmark_validation'], plots_dir)
        
        # 2. 不确定性分析图
        if 'uncertainty_analysis' in validation_results:
            self._plot_uncertainty_analysis(validation_results['uncertainty_analysis'], plots_dir)
        
        # 3. 验证汇总图
        self._plot_validation_summary(validation_results.get('validation_summary', {}), plots_dir)
    
    def _plot_benchmark_results(self, benchmark_data: Dict[str, Any], plots_dir: Path) -> None:
        """绘制基准测试结果图"""
        if 'individual_results' not in benchmark_data:
            return
        
        individual_results = benchmark_data['individual_results']
        case_names = list(individual_results.keys())
        scores = [result.get('score', 0.0) for result in individual_results.values()]
        
        plt.figure(figsize=(10, 6))
        bars = plt.bar(case_names, scores)
        
        # 颜色编码
        for i, (bar, score) in enumerate(zip(bars, scores)):
            if score > 0.8:
                bar.set_color('green')
            elif score > 0.6:
                bar.set_color('blue')
            elif score > 0.4:
                bar.set_color('orange')
            else:
                bar.set_color('red')
        
        plt.title('基准测试结果')
        plt.xlabel('测试案例')
        plt.ylabel('验证分数')
        plt.xticks(rotation=45, ha='right')
        plt.ylim(0, 1)
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        
        plt.savefig(plots_dir / f'benchmark_results.{self.plot_format}', 
                   dpi=self.plot_dpi, bbox_inches='tight')
        plt.close()
    
    def _plot_uncertainty_analysis(self, uncertainty_data: Dict[str, Any], plots_dir: Path) -> None:
        """绘制不确定性分析图"""
        if 'monte_carlo' not in uncertainty_data:
            return
        
        mc_results = uncertainty_data['monte_carlo']
        param_names = list(mc_results.keys())
        uncertainties = [result.get('uncertainty_percentage', 0.0) for result in mc_results.values()]
        
        plt.figure(figsize=(10, 6))
        bars = plt.bar(param_names, uncertainties)
        
        # 颜色编码
        for bar, uncertainty in zip(bars, uncertainties):
            if uncertainty < 5:
                bar.set_color('green')
            elif uncertainty < 15:
                bar.set_color('orange')
            else:
                bar.set_color('red')
        
        plt.title('不确定性分析结果')
        plt.xlabel('输出参数')
        plt.ylabel('不确定性 (%)')
        plt.xticks(rotation=45, ha='right')
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        
        plt.savefig(plots_dir / f'uncertainty_analysis.{self.plot_format}', 
                   dpi=self.plot_dpi, bbox_inches='tight')
        plt.close()
    
    def _plot_validation_summary(self, summary_data: Dict[str, Any], plots_dir: Path) -> None:
        """绘制验证汇总图"""
        overall_score = summary_data.get('overall_validation_score', 0.0)
        
        # 创建仪表盘样式的图
        fig, ax = plt.subplots(figsize=(8, 6), subplot_kw=dict(projection='polar'))
        
        theta = np.linspace(0, 2*np.pi, 100)
        r = np.ones_like(theta)
        
        # 背景圆环
        ax.fill_between(theta, 0, r, alpha=0.1, color='gray')
        
        # 分数扇形
        score_theta = np.linspace(0, 2*np.pi * overall_score, 100)
        score_r = np.ones_like(score_theta)
        
        if overall_score > 0.8:
            color = 'green'
        elif overall_score > 0.6:
            color = 'blue'
        elif overall_score > 0.4:
            color = 'orange'
        else:
            color = 'red'
        
        ax.fill_between(score_theta, 0, score_r, alpha=0.7, color=color)
        
        # 设置
        ax.set_ylim(0, 1)
        ax.set_title(f'总体验证分数: {overall_score:.3f}', pad=20)
        ax.set_rticks([])
        ax.set_thetagrids([])
        
        plt.savefig(plots_dir / f'validation_summary.{self.plot_format}', 
                   dpi=self.plot_dpi, bbox_inches='tight')
        plt.close()
    
    def _generate_main_report(self, validation_results: Dict[str, Any],
                            report_dir: Path, report_title: str) -> Path:
        """生成主报告文件"""
        main_report_file = report_dir / "main_report.md"
        
        with open(main_report_file, 'w', encoding='utf-8') as f:
            f.write(f"# {report_title}\n\n")
            f.write(f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            # 目录
            f.write("## 目录\n\n")
            f.write("1. [执行摘要](executive_summary.md)\n")
            f.write("2. [不确定性分析](uncertainty_analysis.md)\n")
            f.write("3. [基准测试验证](benchmark_validation.md)\n")
            f.write("4. [统计分析](statistical_analysis.md)\n")
            if self.enable_plots:
                f.write("5. [图表](plots/)\n")
            f.write("\n")
            
            # 快速概览
            summary = validation_results.get('validation_summary', {})
            overall_score = summary.get('overall_validation_score', 0.0)
            
            f.write("## 快速概览\n\n")
            f.write(f"**总体验证分数**: {overall_score:.3f}/1.000\n\n")
            
            if overall_score > 0.8:
                f.write("🎉 **验证状态**: 优秀 - 模型表现出色，可用于生产环境\n\n")
            elif overall_score > 0.6:
                f.write("✅ **验证状态**: 良好 - 模型性能良好，建议进一步优化\n\n")
            elif overall_score > 0.4:
                f.write("⚠️ **验证状态**: 可接受 - 模型基本可用，需要改进\n\n")
            else:
                f.write("❌ **验证状态**: 需要改进 - 模型存在重大问题，不建议使用\n\n")
            
            # 详细信息链接
            f.write("详细分析请参阅各专项报告。\n")
        
        return main_report_file
