"""
验证框架模块
============

提供完整的学术验证功能，包括：
- 不确定性量化
- <PERSON> Carlo分析
- Richardson外推
- 基准测试
- 多求解器对比验证
- 性能基准测试
- 自动化验证报告生成
"""

from .academic_validation import AcademicValidationFramework
from .uncertainty_quantification import UncertaintyQuantifier, MonteCarloAnalyzer, StatisticalValidator
from .benchmark_tests import BenchmarkTestSuite, MultiSolverComparison
from .performance_benchmarks import PerformanceBenchmark, PerformanceMetrics
from .validation_reports import ValidationReportGenerator

__all__ = [
    'AcademicValidationFramework',
    'UncertaintyQuantifier',
    'MonteCarloAnalyzer',
    'StatisticalValidator',
    'BenchmarkTestSuite',
    'MultiSolverComparison',
    'PerformanceBenchmark',
    'PerformanceMetrics',
    'ValidationReportGenerator'
]
