"""
噪声传播算法模块
================

基于advice_detailed.md要求，实现完整的噪声传播功能：
- 自由场传播
- 地面反射效应
- 大气吸收修正
- 多普勒效应处理
"""

import numpy as np
import warnings
from typing import Dict, Any, Optional, Tuple, List
from dataclasses import dataclass


@dataclass
class EnvironmentConditions:
    """环境条件"""
    temperature: float = 20.0  # 温度 [°C]
    humidity: float = 50.0     # 相对湿度 [%]
    pressure: float = 101325.0 # 大气压力 [Pa]
    ground_impedance: complex = complex(1.5, 0.1)  # 地面阻抗


@dataclass
class SourceMotion:
    """声源运动参数"""
    velocity: np.ndarray  # 速度矢量 [m/s]
    acceleration: np.ndarray  # 加速度矢量 [m/s²]


class NoisePropagationModel:
    """噪声传播模型"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化噪声传播模型
        
        Args:
            config: 配置参数
        """
        self.config = config
        
        # 基本参数
        self.sound_speed = config.get('sound_speed', 343.0)  # 声速 [m/s]
        self.reference_distance = config.get('reference_distance', 1.0)  # 参考距离 [m]
        
        # 传播效应开关
        self.enable_ground_reflection = config.get('enable_ground_reflection', True)
        self.enable_atmospheric_absorption = config.get('enable_atmospheric_absorption', True)
        self.enable_doppler_effects = config.get('enable_doppler_effects', True)
        self.enable_scattering = config.get('enable_scattering', False)
        
        print(f"✅ 噪声传播模型初始化完成")
        print(f"   地面反射: {'启用' if self.enable_ground_reflection else '禁用'}")
        print(f"   大气吸收: {'启用' if self.enable_atmospheric_absorption else '禁用'}")
        print(f"   多普勒效应: {'启用' if self.enable_doppler_effects else '禁用'}")
    
    def propagate_acoustic_waves(self, source_data: Dict[str, Any], 
                               observer_positions: np.ndarray,
                               environment: EnvironmentConditions,
                               source_motion: Optional[SourceMotion] = None) -> Dict[str, Any]:
        """
        声波传播计算主函数
        
        Args:
            source_data: 声源数据
            observer_positions: 观测点位置 [N, 3]
            environment: 环境条件
            source_motion: 声源运动参数
            
        Returns:
            propagation_results: 传播结果
        """
        frequencies = source_data.get('frequencies', np.array([]))
        source_spl = source_data.get('spl_spectrum', np.array([]))
        source_position = source_data.get('position', np.array([0.0, 0.0, 0.0]))
        
        if len(frequencies) == 0 or len(source_spl) == 0:
            raise ValueError("声源数据不完整")
        
        propagation_results = {
            'frequencies': frequencies,
            'observer_spectra': [],
            'propagation_effects': {}
        }
        
        # 对每个观测点计算传播效应
        for i, observer_pos in enumerate(observer_positions):
            # 1. 自由场传播
            free_field_spl = self._calculate_free_field_propagation(
                source_spl, source_position, observer_pos
            )
            
            # 2. 地面反射效应
            if self.enable_ground_reflection:
                ground_reflected_spl = self._calculate_ground_reflection_effects(
                    free_field_spl, frequencies, source_position, observer_pos, environment
                )
            else:
                ground_reflected_spl = free_field_spl
            
            # 3. 大气吸收修正
            if self.enable_atmospheric_absorption:
                absorbed_spl = self._calculate_atmospheric_absorption(
                    ground_reflected_spl, frequencies, source_position, observer_pos, environment
                )
            else:
                absorbed_spl = ground_reflected_spl
            
            # 4. 多普勒效应处理
            if self.enable_doppler_effects and source_motion is not None:
                doppler_corrected_spl, doppler_frequencies = self._calculate_doppler_effects(
                    absorbed_spl, frequencies, source_position, observer_pos, source_motion
                )
            else:
                doppler_corrected_spl = absorbed_spl
                doppler_frequencies = frequencies
            
            # 5. 散射效应（如果启用）
            if self.enable_scattering:
                final_spl = self._calculate_scattering_effects(
                    doppler_corrected_spl, doppler_frequencies, source_position, observer_pos
                )
            else:
                final_spl = doppler_corrected_spl
            
            propagation_results['observer_spectra'].append({
                'observer_index': i,
                'observer_position': observer_pos.copy(),
                'frequencies': doppler_frequencies.copy(),
                'spl_spectrum': final_spl.copy(),
                'distance': np.linalg.norm(observer_pos - source_position)
            })
        
        return propagation_results
    
    def _calculate_free_field_propagation(self, source_spl: np.ndarray,
                                        source_position: np.ndarray,
                                        observer_position: np.ndarray) -> np.ndarray:
        """计算自由场传播"""
        # 计算距离
        distance = np.linalg.norm(observer_position - source_position)
        
        # 球面扩散衰减
        if distance > 0:
            distance_attenuation = 20 * np.log10(distance / self.reference_distance)
            free_field_spl = source_spl - distance_attenuation
        else:
            free_field_spl = source_spl.copy()
        
        return free_field_spl
    
    def _calculate_ground_reflection_effects(self, spl_spectrum: np.ndarray,
                                           frequencies: np.ndarray,
                                           source_position: np.ndarray,
                                           observer_position: np.ndarray,
                                           environment: EnvironmentConditions) -> np.ndarray:
        """计算地面反射效应"""
        # 计算直达路径和反射路径
        source_height = source_position[2]
        observer_height = observer_position[2]
        horizontal_distance = np.linalg.norm(observer_position[:2] - source_position[:2])
        
        # 直达距离
        direct_distance = np.linalg.norm(observer_position - source_position)
        
        # 反射距离（镜像源方法）
        mirror_source = source_position.copy()
        mirror_source[2] = -source_height
        reflected_distance = np.linalg.norm(observer_position - mirror_source)
        
        # 路径差
        path_difference = reflected_distance - direct_distance
        
        # 地面反射系数（频率相关）
        reflected_spl = np.zeros_like(spl_spectrum)
        
        for i, freq in enumerate(frequencies):
            # 计算入射角
            if horizontal_distance > 0:
                grazing_angle = np.arctan((source_height + observer_height) / horizontal_distance)
            else:
                grazing_angle = np.pi / 2
            
            # 地面反射系数（简化模型）
            reflection_coefficient = self._calculate_ground_reflection_coefficient(
                freq, grazing_angle, environment.ground_impedance
            )
            
            # 相位差
            phase_difference = 2 * np.pi * freq * path_difference / self.sound_speed
            
            # 反射波幅度
            reflected_amplitude = abs(reflection_coefficient)
            reflected_phase = np.angle(reflection_coefficient) + phase_difference
            
            # 合成直达波和反射波
            direct_amplitude = 10**(spl_spectrum[i] / 20)
            total_amplitude = abs(direct_amplitude + reflected_amplitude * np.exp(1j * reflected_phase))
            
            if total_amplitude > 0:
                reflected_spl[i] = 20 * np.log10(total_amplitude)
            else:
                reflected_spl[i] = spl_spectrum[i] - 60  # 很小的值
        
        return reflected_spl
    
    def _calculate_ground_reflection_coefficient(self, frequency: float, 
                                               grazing_angle: float,
                                               ground_impedance: complex) -> complex:
        """计算地面反射系数"""
        # 归一化地面阻抗
        Z_g = ground_impedance
        
        # 反射系数（平面波近似）
        cos_theta = np.cos(grazing_angle)
        sin_theta = np.sin(grazing_angle)
        
        # 垂直极化反射系数
        R_v = (Z_g * cos_theta - 1) / (Z_g * cos_theta + 1)
        
        return R_v
    
    def _calculate_atmospheric_absorption(self, spl_spectrum: np.ndarray,
                                        frequencies: np.ndarray,
                                        source_position: np.ndarray,
                                        observer_position: np.ndarray,
                                        environment: EnvironmentConditions) -> np.ndarray:
        """计算大气吸收修正"""
        distance = np.linalg.norm(observer_position - source_position)
        
        absorbed_spl = np.zeros_like(spl_spectrum)
        
        for i, freq in enumerate(frequencies):
            # 计算大气吸收系数（基于ISO 9613-1）
            absorption_coefficient = self._calculate_atmospheric_absorption_coefficient(
                freq, environment.temperature, environment.humidity
            )
            
            # 应用吸收
            absorption_loss = absorption_coefficient * distance / 1000  # 转换为km
            absorbed_spl[i] = spl_spectrum[i] - absorption_loss
        
        return absorbed_spl
    
    def _calculate_atmospheric_absorption_coefficient(self, frequency: float,
                                                    temperature: float,
                                                    humidity: float) -> float:
        """计算大气吸收系数（dB/km）"""
        # 基于ISO 9613-1标准
        T = temperature + 273.15  # 转换为开尔文
        
        # 氧气吸收
        f_rO = 24 + 4.04e4 * humidity * (0.02 + humidity) / (0.391 + humidity)
        alpha_O = (8.686 * frequency**2 * 
                  (1.84e-11 * (T/293.15)**(-0.5) +
                   (T/293.15)**(-2.5) * 0.01275 * np.exp(-2239.1/T) / 
                   (f_rO + frequency**2/f_rO)))
        
        # 氮气吸收
        f_rN = ((T/293.15)**(-0.5) * 
                (9 + 280 * humidity * np.exp(-4.17 * ((T/293.15)**(-1/3) - 1))))
        alpha_N = (8.686 * frequency**2 * (T/293.15)**(-2.5) * 
                  0.1068 * np.exp(-3352/T) / (f_rN + frequency**2/f_rN))
        
        return alpha_O + alpha_N
    
    def _calculate_doppler_effects(self, spl_spectrum: np.ndarray,
                                 frequencies: np.ndarray,
                                 source_position: np.ndarray,
                                 observer_position: np.ndarray,
                                 source_motion: SourceMotion) -> Tuple[np.ndarray, np.ndarray]:
        """计算多普勒效应"""
        # 计算源到观测点的单位矢量
        r_vector = observer_position - source_position
        distance = np.linalg.norm(r_vector)
        
        if distance == 0:
            return spl_spectrum.copy(), frequencies.copy()
        
        r_unit = r_vector / distance
        
        # 径向速度分量
        v_radial = np.dot(source_motion.velocity, r_unit)
        
        # 多普勒频移因子
        doppler_factor = 1.0 / (1.0 - v_radial / self.sound_speed)
        
        # 频率修正
        doppler_frequencies = frequencies * doppler_factor
        
        # 幅度修正（考虑频率变化对功率的影响）
        doppler_spl = spl_spectrum + 10 * np.log10(abs(doppler_factor))
        
        return doppler_spl, doppler_frequencies
    
    def _calculate_scattering_effects(self, spl_spectrum: np.ndarray,
                                    frequencies: np.ndarray,
                                    source_position: np.ndarray,
                                    observer_position: np.ndarray) -> np.ndarray:
        """计算散射效应（简化实现）"""
        # 简化的散射模型
        distance = np.linalg.norm(observer_position - source_position)
        
        scattered_spl = np.zeros_like(spl_spectrum)
        
        for i, freq in enumerate(frequencies):
            # 简化的散射损失
            scattering_loss = 0.1 * np.log10(freq / 1000) * distance / 1000
            scattered_spl[i] = spl_spectrum[i] - max(0, scattering_loss)
        
        return scattered_spl


def create_standard_environment() -> EnvironmentConditions:
    """创建标准环境条件"""
    return EnvironmentConditions(
        temperature=20.0,
        humidity=50.0,
        pressure=101325.0,
        ground_impedance=complex(1.5, 0.1)
    )
