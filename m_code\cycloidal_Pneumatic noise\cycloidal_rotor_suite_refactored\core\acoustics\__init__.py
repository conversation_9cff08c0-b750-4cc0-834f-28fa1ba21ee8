"""
声学分析模块
==========

提供多保真度声学分析求解器实现，包括：
- FW-H求解器
- BPM噪声模型
- BVI噪声模型
- 噪声传播算法
"""

from .base import AcousticSolverBase
from .solvers import FWHSolver, BPMSolver
from .bpm_noise_model import BPMNoiseModel
from .bvi_noise_model import BVINoiseModel
from .noise_propagation import NoisePropagationModel, EnvironmentConditions, SourceMotion

__all__ = [
    "AcousticSolverBase",
    "FWHSolver",
    "BPMSolver",
    "BPMNoiseModel",
    "BVINoiseModel",
    "NoisePropagationModel",
    "EnvironmentConditions",
    "SourceMotion"
]
