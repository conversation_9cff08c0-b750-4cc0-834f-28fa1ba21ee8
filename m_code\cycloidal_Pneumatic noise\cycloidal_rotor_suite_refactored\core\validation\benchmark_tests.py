"""
基准测试模块
============

基于advice_detailed.md要求，实现完整的基准测试功能：
- 标准验证案例库
- 实验数据对比
- 多求解器验证
- 自动化测试流程
"""

import numpy as np
import warnings
from typing import Dict, Any, Optional, List, Tuple
from dataclasses import dataclass
from pathlib import Path
import json
import logging


@dataclass
class BenchmarkCase:
    """基准测试案例"""
    name: str
    description: str
    configuration: Dict[str, Any]
    experimental_data: Optional[Dict[str, np.ndarray]] = None
    reference_solution: Optional[Dict[str, float]] = None
    tolerance: Dict[str, float] = None
    
    def __post_init__(self):
        if self.tolerance is None:
            self.tolerance = {'default': 0.05}  # 默认5%误差


@dataclass
class ValidationResult:
    """验证结果"""
    case_name: str
    computed_values: Dict[str, float]
    reference_values: Dict[str, float]
    relative_errors: Dict[str, float]
    absolute_errors: Dict[str, float]
    passed: bool
    validation_score: float


class BenchmarkTestSuite:
    """基准测试套件"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化基准测试套件
        
        Args:
            config: 配置参数
        """
        self.test_cases = {}
        self.validation_results = {}
        self.logger = logging.getLogger(__name__)
        
        # 配置参数
        self.default_tolerance = config.get('default_tolerance', 0.05)
        self.enable_experimental_validation = config.get('enable_experimental_validation', True)
        self.enable_cross_validation = config.get('enable_cross_validation', True)
        
        # 加载标准测试案例
        self._load_standard_test_cases()
        
        print(f"✅ 基准测试套件初始化完成")
        print(f"   测试案例数: {len(self.test_cases)}")
        print(f"   默认容差: {self.default_tolerance*100:.1f}%")
        print(f"   实验验证: {'启用' if self.enable_experimental_validation else '禁用'}")
    
    def _load_standard_test_cases(self) -> None:
        """加载标准测试案例"""
        # 循环翼转子标准案例
        self.add_test_case(BenchmarkCase(
            name="cycloidal_rotor_hover",
            description="循环翼转子悬停状态验证",
            configuration={
                'rotor_radius': 1.0,
                'blade_count': 3,
                'rotor_rpm': 1500.0,
                'collective_pitch': 0.0,
                'cyclic_pitch': 0.0,
                'inflow_velocity': 0.0
            },
            reference_solution={
                'thrust_coefficient': 0.008,
                'power_coefficient': 0.0012,
                'figure_of_merit': 0.65
            },
            tolerance={
                'thrust_coefficient': 0.10,
                'power_coefficient': 0.15,
                'figure_of_merit': 0.20
            }
        ))
        
        self.add_test_case(BenchmarkCase(
            name="cycloidal_rotor_forward_flight",
            description="循环翼转子前飞状态验证",
            configuration={
                'rotor_radius': 1.0,
                'blade_count': 3,
                'rotor_rpm': 1200.0,
                'collective_pitch': 5.0,
                'cyclic_pitch': 3.0,
                'inflow_velocity': 20.0,
                'advance_ratio': 0.3
            },
            reference_solution={
                'thrust_coefficient': 0.012,
                'power_coefficient': 0.0018,
                'propulsive_efficiency': 0.75
            },
            tolerance={
                'thrust_coefficient': 0.12,
                'power_coefficient': 0.18,
                'propulsive_efficiency': 0.15
            }
        ))
        
        # 动态失速验证案例
        self.add_test_case(BenchmarkCase(
            name="dynamic_stall_validation",
            description="动态失速模型验证",
            configuration={
                'airfoil': 'NACA0012',
                'chord': 0.1,
                'reynolds_number': 1e6,
                'mach_number': 0.1,
                'alpha_mean': 10.0,
                'alpha_amplitude': 10.0,
                'reduced_frequency': 0.1
            },
            reference_solution={
                'max_lift_coefficient': 1.8,
                'stall_angle': 15.0,
                'hysteresis_area': 0.15
            },
            tolerance={
                'max_lift_coefficient': 0.15,
                'stall_angle': 0.20,
                'hysteresis_area': 0.25
            }
        ))
        
        # 声学验证案例
        self.add_test_case(BenchmarkCase(
            name="bpm_noise_validation",
            description="BPM噪声模型验证",
            configuration={
                'airfoil': 'NACA0012',
                'chord': 0.1,
                'velocity': 50.0,
                'angle_of_attack': 5.0,
                'observer_distance': 10.0,
                'frequency_range': [100, 10000]
            },
            reference_solution={
                'overall_spl': 65.0,
                'peak_frequency': 1000.0,
                'tbl_te_contribution': 0.6
            },
            tolerance={
                'overall_spl': 0.10,
                'peak_frequency': 0.20,
                'tbl_te_contribution': 0.15
            }
        ))
    
    def add_test_case(self, test_case: BenchmarkCase) -> None:
        """添加测试案例"""
        self.test_cases[test_case.name] = test_case
        self.logger.info(f"添加测试案例: {test_case.name}")
    
    def run_validation_suite(self, solver_function: callable) -> Dict[str, ValidationResult]:
        """
        运行完整验证套件
        
        Args:
            solver_function: 求解器函数
            
        Returns:
            验证结果字典
        """
        self.logger.info("开始运行基准测试套件")
        
        results = {}
        
        for case_name, test_case in self.test_cases.items():
            self.logger.info(f"运行测试案例: {case_name}")
            
            try:
                result = self.run_single_test(test_case, solver_function)
                results[case_name] = result
                
                status = "✅ 通过" if result.passed else "❌ 失败"
                score = result.validation_score
                self.logger.info(f"  {status} (分数: {score:.2f})")
                
            except Exception as e:
                self.logger.error(f"测试案例 {case_name} 运行失败: {e}")
                results[case_name] = ValidationResult(
                    case_name=case_name,
                    computed_values={},
                    reference_values=test_case.reference_solution or {},
                    relative_errors={},
                    absolute_errors={},
                    passed=False,
                    validation_score=0.0
                )
        
        self.validation_results = results
        self._generate_summary_report()
        
        return results
    
    def run_single_test(self, test_case: BenchmarkCase, 
                       solver_function: callable) -> ValidationResult:
        """运行单个测试案例"""
        # 运行求解器
        computed_result = solver_function(test_case.configuration)
        
        if test_case.reference_solution is None:
            # 如果没有参考解，只记录计算结果
            return ValidationResult(
                case_name=test_case.name,
                computed_values=computed_result,
                reference_values={},
                relative_errors={},
                absolute_errors={},
                passed=True,
                validation_score=1.0
            )
        
        # 计算误差
        relative_errors = {}
        absolute_errors = {}
        passed_checks = []
        
        for key, reference_value in test_case.reference_solution.items():
            if key in computed_result:
                computed_value = computed_result[key]
                
                # 绝对误差
                abs_error = abs(computed_value - reference_value)
                absolute_errors[key] = abs_error
                
                # 相对误差
                if abs(reference_value) > 1e-12:
                    rel_error = abs_error / abs(reference_value)
                    relative_errors[key] = rel_error
                    
                    # 检查是否通过容差测试
                    tolerance = test_case.tolerance.get(key, self.default_tolerance)
                    passed = rel_error <= tolerance
                    passed_checks.append(passed)
                    
                    self.logger.debug(f"  {key}: 计算值={computed_value:.6f}, "
                                    f"参考值={reference_value:.6f}, "
                                    f"相对误差={rel_error:.3f}, "
                                    f"容差={tolerance:.3f}, "
                                    f"通过={passed}")
                else:
                    relative_errors[key] = float('inf') if abs_error > 1e-12 else 0.0
                    passed_checks.append(abs_error <= 1e-12)
        
        # 计算验证分数
        if passed_checks:
            validation_score = sum(passed_checks) / len(passed_checks)
            overall_passed = all(passed_checks)
        else:
            validation_score = 0.0
            overall_passed = False
        
        return ValidationResult(
            case_name=test_case.name,
            computed_values=computed_result,
            reference_values=test_case.reference_solution,
            relative_errors=relative_errors,
            absolute_errors=absolute_errors,
            passed=overall_passed,
            validation_score=validation_score
        )
    
    def _generate_summary_report(self) -> None:
        """生成汇总报告"""
        if not self.validation_results:
            return
        
        total_tests = len(self.validation_results)
        passed_tests = sum(1 for r in self.validation_results.values() if r.passed)
        average_score = np.mean([r.validation_score for r in self.validation_results.values()])
        
        print(f"\n📊 基准测试汇总报告")
        print(f"=" * 50)
        print(f"总测试数: {total_tests}")
        print(f"通过测试: {passed_tests}")
        print(f"通过率: {passed_tests/total_tests*100:.1f}%")
        print(f"平均分数: {average_score:.3f}")
        print(f"=" * 50)
        
        # 详细结果
        for case_name, result in self.validation_results.items():
            status = "✅" if result.passed else "❌"
            print(f"{status} {case_name}: {result.validation_score:.3f}")
    
    def get_validation_summary(self) -> Dict[str, Any]:
        """获取验证汇总信息"""
        if not self.validation_results:
            return {}
        
        total_tests = len(self.validation_results)
        passed_tests = sum(1 for r in self.validation_results.values() if r.passed)
        average_score = np.mean([r.validation_score for r in self.validation_results.values()])
        
        return {
            'total_tests': total_tests,
            'passed_tests': passed_tests,
            'pass_rate': passed_tests / total_tests,
            'average_score': average_score,
            'individual_results': {
                name: {
                    'passed': result.passed,
                    'score': result.validation_score,
                    'relative_errors': result.relative_errors
                }
                for name, result in self.validation_results.items()
            }
        }
    
    def export_results(self, output_path: str) -> None:
        """导出验证结果"""
        summary = self.get_validation_summary()
        
        output_file = Path(output_path)
        output_file.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(summary, f, indent=2, ensure_ascii=False)
        
        self.logger.info(f"验证结果已导出到: {output_file}")


class MultiSolverComparison:
    """多求解器对比验证"""

    def __init__(self, config: Dict[str, Any]):
        """初始化多求解器对比验证"""
        self.config = config
        self.solver_results = {}
        self.comparison_metrics = {}

        # 对比参数
        self.correlation_threshold = config.get('correlation_threshold', 0.9)
        self.max_difference_threshold = config.get('max_difference_threshold', 0.15)

        print(f"✅ 多求解器对比验证初始化完成")
        print(f"   相关性阈值: {self.correlation_threshold}")
        print(f"   最大差异阈值: {self.max_difference_threshold}")

    def add_solver_result(self, solver_name: str, solver_type: str,
                         results: Dict[str, float], performance_metrics: Dict[str, float]) -> None:
        """添加求解器结果"""
        self.solver_results[solver_name] = {
            'solver_type': solver_type,
            'results': results,
            'performance': performance_metrics
        }

    def run_cross_validation(self) -> Dict[str, Any]:
        """运行多求解器交叉验证"""
        if len(self.solver_results) < 2:
            return {'error': 'Need at least 2 solvers for comparison'}

        solver_names = list(self.solver_results.keys())
        comparison_results = {}

        # 两两对比
        for i in range(len(solver_names)):
            for j in range(i + 1, len(solver_names)):
                solver1, solver2 = solver_names[i], solver_names[j]

                comparison_key = f"{solver1}_vs_{solver2}"
                comparison_results[comparison_key] = self._compare_two_solvers(solver1, solver2)

        # 计算整体一致性
        overall_consistency = self._calculate_overall_consistency(comparison_results)

        return {
            'pairwise_comparisons': comparison_results,
            'overall_consistency': overall_consistency,
            'solver_ranking': self._rank_solvers()
        }

    def _compare_two_solvers(self, solver1: str, solver2: str) -> Dict[str, Any]:
        """对比两个求解器"""
        results1 = self.solver_results[solver1]['results']
        results2 = self.solver_results[solver2]['results']

        # 找到共同的输出参数
        common_keys = set(results1.keys()) & set(results2.keys())

        if not common_keys:
            return {'error': 'No common output parameters'}

        correlations = {}
        relative_differences = {}

        for key in common_keys:
            val1, val2 = results1[key], results2[key]

            # 相对差异
            if abs(val1) > 1e-12:
                rel_diff = abs(val1 - val2) / abs(val1)
            else:
                rel_diff = abs(val1 - val2)

            relative_differences[key] = rel_diff

            # 简化的相关性（基于相对差异）
            correlation = 1.0 - min(rel_diff, 1.0)
            correlations[key] = correlation

        # 计算平均指标
        avg_correlation = np.mean(list(correlations.values()))
        max_difference = np.max(list(relative_differences.values()))

        # 判断是否通过
        passed = (avg_correlation >= self.correlation_threshold and
                 max_difference <= self.max_difference_threshold)

        return {
            'correlations': correlations,
            'relative_differences': relative_differences,
            'avg_correlation': avg_correlation,
            'max_difference': max_difference,
            'passed': passed,
            'status': 'PASS' if passed else 'FAIL'
        }

    def _calculate_overall_consistency(self, comparison_results: Dict[str, Any]) -> Dict[str, Any]:
        """计算整体一致性"""
        if not comparison_results:
            return {'consistency_score': 0.0, 'status': 'FAIL'}

        # 计算平均一致性分数
        correlations = []
        max_differences = []

        for comp_result in comparison_results.values():
            if 'avg_correlation' in comp_result:
                correlations.append(comp_result['avg_correlation'])
                max_differences.append(comp_result['max_difference'])

        if not correlations:
            return {'consistency_score': 0.0, 'status': 'FAIL'}

        avg_correlation = np.mean(correlations)
        avg_max_difference = np.mean(max_differences)

        # 综合一致性分数
        consistency_score = avg_correlation * (1.0 - avg_max_difference)

        # 判断状态
        if consistency_score > 0.8:
            status = 'EXCELLENT'
        elif consistency_score > 0.6:
            status = 'GOOD'
        elif consistency_score > 0.4:
            status = 'ACCEPTABLE'
        else:
            status = 'POOR'

        return {
            'consistency_score': consistency_score,
            'avg_correlation': avg_correlation,
            'avg_max_difference': avg_max_difference,
            'status': status
        }

    def _rank_solvers(self) -> List[Dict[str, Any]]:
        """对求解器进行排名"""
        rankings = []

        for solver_name, solver_data in self.solver_results.items():
            performance = solver_data['performance']

            # 计算综合分数（精度 + 性能）
            accuracy_score = 1.0  # 默认值，实际应基于验证结果
            speed_score = 1.0 / max(performance.get('cpu_time', 1.0), 0.1)
            memory_score = 1.0 / max(performance.get('memory_usage', 100.0), 10.0)

            # 综合分数
            overall_score = 0.5 * accuracy_score + 0.3 * speed_score + 0.2 * memory_score

            rankings.append({
                'solver_name': solver_name,
                'solver_type': solver_data['solver_type'],
                'overall_score': overall_score,
                'accuracy_score': accuracy_score,
                'speed_score': speed_score,
                'memory_score': memory_score
            })

        # 按综合分数排序
        rankings.sort(key=lambda x: x['overall_score'], reverse=True)

        return rankings


def create_standard_benchmark_suite(config: Optional[Dict[str, Any]] = None) -> BenchmarkTestSuite:
    """创建标准基准测试套件"""
    if config is None:
        config = {
            'default_tolerance': 0.05,
            'enable_experimental_validation': True,
            'enable_cross_validation': True
        }

    return BenchmarkTestSuite(config)
