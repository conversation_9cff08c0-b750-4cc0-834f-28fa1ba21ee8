"""
BEMT求解器实现 - 完整版本
========================

基于原始cycloidal_rotor_suite项目的完整BEMT实现
保留所有核心算法、物理修正和数值方法
"""

import numpy as np
import time
from typing import Dict, Any, Optional, Tuple, List
from ...interfaces.solver_interface import SolverInterface, SolverResults, SolverConfig, FidelityLevel, SolverType
from ..base import AerodynamicSolverBase


class BladeElementWithLB:
    """带有L-B动态失速模型的桨叶单元"""

    def __init__(self, blade_idx: int, elem_idx: int, radial_position: float,
                 chord: float, lb_model=None):
        """
        初始化桨叶单元

        Args:
            blade_idx: 桨叶索引
            elem_idx: 单元索引
            radial_position: 径向位置 [m]
            chord: 弦长 [m]
            lb_model: L-B动态失速模型
        """
        self.blade_idx = blade_idx
        self.elem_idx = elem_idx
        self.radial_position = radial_position
        self.chord = chord
        self.lb_model = lb_model

        # 状态历史
        self.alpha_history = []
        self.time_history = []
        self.coefficient_history = []

    def calculate_unsteady_coefficients(self, alpha_eff: float, alpha_dot: float,
                                      V_local: float, dt: float, t: float, **kwargs) -> Tuple[float, float, float]:
        """
        计算非定常气动系数

        Args:
            alpha_eff: 有效攻角 [rad]
            alpha_dot: 攻角变化率 [rad/s]
            V_local: 局部速度 [m/s]
            dt: 时间步长 [s]
            t: 当前时间 [s]
            **kwargs: 循环翼特殊参数

        Returns:
            (Cl, Cd, Cm): 升力系数、阻力系数、力矩系数
        """
        if self.lb_model is None:
            # 回退到静态系数
            return self._get_static_coefficients(alpha_eff)

        try:
            # 使用L-B模型计算动态系数，传递循环翼参数
            Cl, Cd, Cm = self.lb_model.compute_dynamic_coefficients(
                alpha_eff, alpha_dot, V_local, self.chord, dt, **kwargs
            )

            # 记录历史
            self.alpha_history.append(alpha_eff)
            self.time_history.append(t)
            self.coefficient_history.append((Cl, Cd, Cm))

            # 限制历史长度
            max_history = 100
            if len(self.alpha_history) > max_history:
                self.alpha_history.pop(0)
                self.time_history.pop(0)
                self.coefficient_history.pop(0)

            return Cl, Cd, Cm

        except Exception as e:
            print(f"   ⚠️ L-B计算失败 ({self.blade_idx}, {self.elem_idx}): {e}")
            return self._get_static_coefficients(alpha_eff)

    def _get_static_coefficients(self, alpha_eff: float) -> Tuple[float, float, float]:
        """获取静态气动系数（简化实现）"""
        # 简化的静态系数计算
        alpha_deg = np.degrees(alpha_eff)

        # 线性升力曲线（失速前）
        if abs(alpha_deg) < 15.0:
            Cl = 2 * np.pi * alpha_eff  # 理论升力曲线斜率
            Cd = 0.01 + 0.02 * (alpha_eff)**2  # 简化阻力极曲线
        else:
            # 失速后
            Cl = 1.2 * np.sign(alpha_eff)  # 失速后升力系数
            Cd = 0.1 + 0.5 * abs(alpha_eff)  # 失速后阻力增加

        Cm = -0.1 * Cl  # 简化力矩系数

        return Cl, Cd, Cm

class BEMTSolver(AerodynamicSolverBase):
    """
    叶素动量理论(BEMT)求解器 - 完整实现版本
    
    基于原始项目的完整BEMT算法，包含：
    - 完整的诱导速度迭代求解
    - 所有物理修正模型
    - 高级数值方法和收敛加速
    - 循环翼转子特殊处理
    """
    
    def __init__(self, config: SolverConfig):
        """初始化BEMT求解器 - 循环翼优化版本"""
        super().__init__(config)

        # 循环翼专用保真度检查（基于adevice_complement2.md第22-34行建议）
        fidelity_level = config.fidelity_level.value if hasattr(config.fidelity_level, 'value') else str(config.fidelity_level)
        if fidelity_level.lower() == "low":
            raise ValueError("循环翼转子不支持低保真度BEMT - 复杂运动学需要至少中保真度")

        self._fidelity_level = fidelity_level  # 使用私有属性避免与基类冲突
        print(f"✅ BEMT求解器循环翼优化模式: {fidelity_level}保真度")

        # 基本求解参数
        self.n_elements = getattr(config, 'blade_elements_count', 20)
        self.max_iterations = config.max_iterations
        self.convergence_tolerance = config.convergence_tolerance
        self.relaxation_factor = config.solver_specific_params.get('relaxation_factor', 0.5)

        # 循环翼专用物理修正配置（基于adevice_complement2.md建议）
        self.enable_tip_loss_correction = config.solver_specific_params.get('enable_tip_loss', True)
        self.enable_root_loss_correction = config.solver_specific_params.get('enable_hub_loss', True)
        self.enable_3d_rotational_effects = True  # 循环翼必需，强制启用
        self.enable_compressibility_correction = config.solver_specific_params.get('enable_compressibility', False)

        # 动态失速：高保真度强制启用，中保真度可选
        if self._fidelity_level == "high":
            self.enable_dynamic_stall = True  # 高保真度必需
            print("   🔧 高保真度模式：强制启用动态失速模型")
        else:
            self.enable_dynamic_stall = config.solver_specific_params.get('enable_dynamic_stall', True)  # 中保真度默认启用

        # L-B模型配置（基于原始实现）
        self.unsteady_model_name = config.solver_specific_params.get('unsteady_model_name', 'leishman_beddoes')
        self.lb_model_config = {
            'enable_3d_correction': config.solver_specific_params.get('lb_enable_3d_correction', True),
            'enhanced_mode': config.solver_specific_params.get('lb_enhanced_mode', True),
            'enable_gpu_acceleration': config.solver_specific_params.get('lb_enable_gpu', False)
        }

        # 动态失速状态历史
        self.alpha_history = {}  # 攻角历史
        self.time_history = {}   # 时间历史
        self.blade_elements = None  # 桨叶单元存储

        # 循环翼专用3D修正和失速延迟模型
        self.enable_snel_stall_delay = config.solver_specific_params.get('enable_snel_stall_delay', True)
        self.enable_du_selig_radial_flow = config.solver_specific_params.get('enable_du_selig_radial_flow', True)
        self.enable_cycloidal_3d_corrections = True  # 循环翼专用修正，强制启用

        # 高级数值方法
        self.use_aitken_acceleration = config.solver_specific_params.get('use_aitken_acceleration', True)
        self.use_adaptive_relaxation = config.solver_specific_params.get('use_adaptive_relaxation', True)
        self.enable_adaptive_timestep = config.solver_specific_params.get('enable_adaptive_timestep', False)
        
        # 几何参数
        self.R_rotor = None
        self.R_hub = None
        self.B = None  # 桨叶数
        self.c = None  # 弦长
        self.span = None
        self.chord_distribution = None
        self.twist_distribution = None
        
        # 运行时参数
        self.omega_rotor = 0.0
        self.V_inf = np.array([0.0, 0.0, 0.0])
        self.V_inf_x = 0.0
        self.V_inf_y = 0.0
        self.rho = 1.225  # 空气密度
        self.current_time = 0.0
        
        # 径向站位
        self.r_positions = None
        
        # 历史数据存储
        self.velocity_history = {}
        self.alpha_history = {}
        self.convergence_history = []

        # 智能初值猜测系统（基于原始实现）
        self.enable_smart_initial_guess = config.solver_specific_params.get('enable_smart_initial_guess', True)
        self.previous_induced_velocities = None  # 空间插值用

        # 桨叶间干扰效应
        self.use_blade_interaction = config.solver_specific_params.get('use_blade_interaction', False)
        self.blade_states = {}  # 桨叶状态存储

        # 详细载荷历史（用于声学分析）
        self.enable_detailed_loads_history = config.solver_specific_params.get('enable_detailed_loads_history', False)
        self.detailed_loads_history = []

        # 注意：循环翼专用3D修正已在上面配置，此处移除重复配置
        
        # 翼型数据库和插值器
        self.airfoil_database = None
        self.adaptive_interpolator = None
        self.use_adaptive_interpolation = False
        self.use_real_airfoil_data = config.solver_specific_params.get('use_real_airfoil_data', True)
        self.airfoil_name = config.solver_specific_params.get('airfoil_name', 'NACA0012')

        # 初始化翼型数据库
        if self.use_real_airfoil_data:
            self._initialize_airfoil_database()
        
        # 3D力存储（用于声学耦合）
        self.forces_3d = None
        self._last_forces = np.zeros(3)
        
        print(f"✅ BEMT求解器初始化完成")
        print(f"   叶素数量: {self.n_elements}")
        print(f"   最大迭代次数: {self.max_iterations}")
        print(f"   收敛容差: {self.convergence_tolerance:.2e}")
        if self.enable_dynamic_stall:
            print(f"   动态失速模型: {self.unsteady_model_name}")
            print(f"     3D修正: {'启用' if self.lb_model_config['enable_3d_correction'] else '禁用'}")
            print(f"     增强模式: {'启用' if self.lb_model_config['enhanced_mode'] else '禁用'}")
    
    @property
    def solver_name(self) -> str:
        return "BEMT"
    
    @property
    def solver_type(self) -> SolverType:
        return SolverType.AERODYNAMIC
    
    def _create_blade_elements_with_lb_model(self) -> None:
        """创建带有L-B模型的桨叶单元（基于原始实现）"""
        if not self.enable_dynamic_stall or self.unsteady_model_name != 'leishman_beddoes':
            return

        print("   🔧 初始化L-B动态失速模型...")

        self.blade_elements = []

        for blade_idx in range(self.B):
            blade_elements = []
            for elem_idx in range(self.n_elements):
                # 创建带有L-B模型的桨叶单元
                element = self._create_blade_element_with_lb_model(blade_idx, elem_idx)
                blade_elements.append(element)
            self.blade_elements.append(blade_elements)

        print(f"   ✅ 创建了 {self.B} 个桨叶，每个桨叶 {self.n_elements} 个单元")

    def _create_blade_element_with_lb_model(self, blade_idx: int, elem_idx: int):
        """创建单个带有L-B模型的桨叶单元"""
        try:
            from ...physics.dynamic_stall import create_naca0012_model

            # 获取弦长（简化处理）
            chord = self.c if hasattr(self, 'c') and self.c is not None else 0.1

            # 创建L-B模型
            lb_model = create_naca0012_model(
                enhanced_mode=self.lb_model_config['enhanced_mode'],
                enable_cycloidal_corrections=True,
                enable_gpu_acceleration=self.lb_model_config.get('enable_gpu_acceleration', False)
            )

            # 创建桨叶单元对象
            element = BladeElementWithLB(
                blade_idx=blade_idx,
                elem_idx=elem_idx,
                radial_position=self.r_positions[elem_idx] if self.r_positions is not None else 0.5,
                chord=chord,
                lb_model=lb_model
            )

            return element

        except Exception as e:
            print(f"   ⚠️ 创建L-B模型失败 ({blade_idx}, {elem_idx}): {e}")
            # 返回简化的桨叶单元
            return BladeElementWithLB(
                blade_idx=blade_idx,
                elem_idx=elem_idx,
                radial_position=self.r_positions[elem_idx] if self.r_positions is not None else 0.5,
                chord=0.1,
                lb_model=None
            )

    def initialize(self, geometry_data) -> None:
        """初始化求解器几何 - 支持BladeGeometry对象和字典"""
        # 检查输入类型并提取几何参数
        if hasattr(geometry_data, 'radius'):  # BladeGeometry对象
            self.R_rotor = geometry_data.radius
            self.R_hub = geometry_data.hub_radius
            self.B = getattr(geometry_data, 'blade_count', 3)  # 默认3桨叶
            self.span = getattr(geometry_data, 'span', 1.0)
            self.chord_distribution = geometry_data.chord_distribution
            self.twist_distribution = geometry_data.twist_distribution
            self.r_stations = geometry_data.r_stations
            self.num_stations = geometry_data.num_stations
        else:  # 字典格式
            self.R_rotor = geometry_data['rotor_radius']
            self.R_hub = geometry_data.get('hub_radius', 0.1 * self.R_rotor)
            self.B = geometry_data['blade_count']
            self.span = geometry_data.get('blade_span', 1.0)
            self.chord_distribution = geometry_data.get('chord_distribution',
                                                       np.full(self.n_elements, 0.1))
            self.twist_distribution = geometry_data.get('twist_distribution',
                                                       np.zeros(self.n_elements))
            # 创建径向站位
            self.r_stations = np.linspace(self.R_hub, self.R_rotor, self.n_elements)
            self.num_stations = self.n_elements

        # 平均弦长
        self.c = np.mean(self.chord_distribution)

        # 设置径向站位（如果还没有设置）
        if not hasattr(self, 'r_stations') or self.r_stations is None:
            self.r_stations = np.linspace(self.R_hub, self.R_rotor, self.n_elements)
            self.num_stations = self.n_elements

        # 兼容性：设置r_positions为r_stations的别名
        self.r_positions = self.r_stations
        
        # 初始化历史数据
        self.velocity_history = {}
        self.alpha_history = {}
        self.convergence_history = []
        
        # 创建带有L-B模型的桨叶单元
        self._create_blade_elements_with_lb_model()

        self._is_initialized = True
        print(f"BEMT求解器几何初始化完成")
        print(f"   旋翼半径: {self.R_rotor:.3f} m")
        print(f"   桨叶数: {self.B}")
        print(f"   叶素数: {self.n_elements}")

    def initialize_solver(self, geometry_data: Dict[str, Any]) -> None:
        """初始化求解器几何 - 别名方法，用于兼容性"""
        # 转换测试数据格式到标准格式
        standard_geometry = {
            'rotor_radius': geometry_data.get('R_rotor', 1.0),
            'hub_radius': geometry_data.get('hub_radius', 0.1),
            'blade_count': geometry_data.get('blade_count', 3),
            'blade_span': geometry_data.get('blade_span', 1.0),
            'chord_distribution': geometry_data.get('chord_distribution'),
            'twist_distribution': geometry_data.get('twist_distribution'),
            'radial_stations': geometry_data.get('radial_stations')
        }

        # 如果提供了径向站位，使用它们来设置叶素数量
        if standard_geometry['radial_stations'] is not None:
            self.n_elements = len(standard_geometry['radial_stations'])
            self.r_positions = standard_geometry['radial_stations']

        # 调用标准初始化方法
        self.initialize(standard_geometry)

    def solve_timestep(self, time: float, boundary_conditions: Dict[str, Any]) -> SolverResults:
        """求解单个时间步"""
        if not self._is_initialized:
            raise RuntimeError("求解器未初始化")
        
        self.current_time = time
        
        # 提取边界条件
        rotor_rpm = boundary_conditions.get('rotor_rpm', 1500.0)
        self.omega_rotor = rotor_rpm * 2 * np.pi / 60  # 转换为rad/s
        
        self.V_inf = boundary_conditions.get('freestream_velocity', np.array([0.0, 0.0, 0.0]))
        self.V_inf_x = self.V_inf[0]
        self.V_inf_y = self.V_inf[1]
        
        blade_pitch = boundary_conditions.get('blade_pitch', 0.0)

        # 设置桨叶控制参数（用于原始算法）
        self.collective_pitch = blade_pitch
        cyclic_pitch_input = boundary_conditions.get('cyclic_pitch', 0.0)

        # 确保cyclic_pitch是标量值
        if isinstance(cyclic_pitch_input, (list, tuple)):
            # 如果是列表，取第一个值作为主要周期变距
            self.cyclic_pitch = float(cyclic_pitch_input[0]) if len(cyclic_pitch_input) > 0 else 0.0
        else:
            self.cyclic_pitch = float(cyclic_pitch_input)
        
        # BEMT核心求解
        total_forces, total_moments, convergence_achieved, iterations = self._solve_bemt_complete(
            blade_pitch
        )
        
        # 计算压力分布和速度场（简化）
        pressure_distribution = self._compute_pressure_distribution()
        velocity_field = self._compute_velocity_field()
        
        # 创建结果
        result = SolverResults(
            forces=total_forces,
            moments=total_moments,
            pressure_distribution=pressure_distribution,
            velocity_field=velocity_field,
            time_stamp=time,
            convergence_achieved=convergence_achieved,
            iterations_used=iterations,
            computation_time=0.01,  # 简化的计算时间
            solver_specific_data={
                'blade_positions': self.get_blade_positions(),
                'circulation': self.get_circulation_distribution(),
                'induction_factors': self._get_induction_factors(),
                'convergence_history': self.convergence_history[-10:] if self.convergence_history else []
            }
        )
        
        return result
    
    def _solve_bemt_complete(self, blade_pitch: float) -> Tuple[np.ndarray, np.ndarray, bool, int]:
        """完整的BEMT求解算法"""
        total_forces = np.zeros(3)
        total_moments = np.zeros(3)
        
        # 检查是否为静态翼模式
        if abs(self.omega_rotor) < 1e-3:
            return self._solve_static_wing_mode()
        
        # 初始化诱导速度猜测
        v_induced_guess = self._initialize_induced_velocity_guess()
        
        # 主迭代循环
        convergence_achieved = False
        iteration = 0
        
        for iteration in range(self.max_iterations):
            # 计算每个叶素的载荷
            element_forces = []
            element_moments = []
            element_circulation = []
            
            for i, r in enumerate(self.r_positions):
                # 计算每个桨叶在不同方位角的载荷
                blade_forces = np.zeros(3)
                blade_moments = np.zeros(3)
                blade_circulation = 0.0
                
                # 对每个桨叶进行计算
                for blade_idx in range(self.B):
                    blade_phase = blade_idx * 2 * np.pi / self.B
                    theta = self.omega_rotor * self.current_time + blade_phase
                    
                    # 求解该叶素的诱导速度
                    v_induced = self._solve_element_induced_velocity(
                        theta, r, v_induced_guess[i, 1], iteration
                    )
                    
                    # 计算有效攻角
                    alpha_eff = self._calculate_effective_aoa(theta, r, v_induced)
                    
                    # 获取气动系数
                    Cl, Cd = self._get_airfoil_coefficients(alpha_eff, r)
                    
                    # 应用物理修正
                    Cl_corrected, Cd_corrected = self._apply_physical_corrections(
                        r, alpha_eff, Cl, Cd
                    )
                    
                    # 计算叶素载荷
                    force, moment, circulation = self._calculate_element_loads(
                        r, alpha_eff, Cl_corrected, Cd_corrected, v_induced
                    )
                    
                    blade_forces += force
                    blade_moments += moment
                    blade_circulation += circulation
                
                element_forces.append(blade_forces)
                element_moments.append(blade_moments)
                element_circulation.append(blade_circulation)
            
            # 累加总载荷
            total_forces = np.sum(element_forces, axis=0)
            total_moments = np.sum(element_moments, axis=0)
            
            # 检查收敛性
            if iteration > 0:
                force_change = np.linalg.norm(total_forces - self._last_forces)
                relative_change = force_change / (np.linalg.norm(total_forces) + 1e-12)
                
                self.convergence_history.append(relative_change)
                
                if relative_change < self.convergence_tolerance:
                    convergence_achieved = True
                    break
            
            self._last_forces = total_forces.copy()
            
            # 更新诱导速度猜测
            v_induced_guess = self._update_induced_velocity_guess(
                v_induced_guess, element_forces, iteration
            )
        
        # 存储3D力分布（用于声学耦合）
        self._compute_forces_3d()
        
        return total_forces, total_moments, convergence_achieved, iteration + 1

    def _initialize_induced_velocity_guess(self) -> np.ndarray:
        """初始化诱导速度猜测"""
        v_induced_guess = np.zeros((self.n_elements, 3))

        for i, r in enumerate(self.r_positions):
            element_key = f"r_{r:.3f}"

            # 尝试使用历史数据
            if element_key in self.velocity_history and len(self.velocity_history[element_key]) > 0:
                v_induced_guess[i, 1] = self.velocity_history[element_key][-1]
            else:
                # 基于动量理论的初始估算
                V_tip = self.omega_rotor * r
                if V_tip > 1e-6:
                    v_induced_guess[i, 1] = 0.1 * V_tip
                else:
                    v_induced_guess[i, 1] = 1.0

        return v_induced_guess

    def _solve_element_induced_velocity(self, theta: float, r: float,
                                      initial_guess: float, iteration: int) -> np.ndarray:
        """求解单个叶素的诱导速度 - 增强版本"""
        # 检查是否为静态翼模式
        if abs(self.omega_rotor) < 1e-3:
            return self._solve_static_wing_mode_element(theta, r)

        # 使用智能初值猜测（如果启用）
        if self.enable_smart_initial_guess and iteration == 0:
            # 找到对应的叶素索引
            elem_idx = self._find_element_index(r)
            blade_idx = 0  # 简化，实际应该从调用处传入
            smart_guess = self._get_smart_initial_guess(blade_idx, elem_idx, theta, r)
            if abs(smart_guess) > abs(initial_guess):
                initial_guess = smart_guess

        # 验证初值猜测
        validated_guess = self._validate_initial_guess(initial_guess, theta, r)

        # 使用不动点迭代求解
        v_induced_y = validated_guess
        converged = False

        # 存储前两次迭代值（用于Aitken加速）
        v_prev = None
        v_current = v_induced_y

        for sub_iter in range(20):  # 子迭代限制
            # 计算有效攻角
            alpha_eff = self._calculate_effective_aoa_with_induced(theta, r, v_induced_y)

            # 获取气动系数（集成L-B动态失速模型）
            Cl, Cd = self._get_airfoil_coefficients_with_dynamic_stall(alpha_eff, r, theta)

            # 应用物理修正
            Cl_corrected, Cd_corrected = self._apply_physical_corrections(r, alpha_eff, Cl, Cd)

            # 计算相对速度
            V_rel = self._get_relative_velocity_magnitude(theta, r, v_induced_y)

            # 基于载荷计算新的诱导速度
            v_induced_new = self._compute_induced_velocity_from_loads(
                Cl_corrected, Cd_corrected, V_rel, r
            )

            # 应用松弛因子
            relaxation = self._get_adaptive_relaxation_factor(sub_iter, iteration)
            v_induced_next = v_current * (1 - relaxation) + v_induced_new * relaxation

            # Aitken加速（如果有足够的历史数据）
            if self.use_aitken_acceleration and v_prev is not None:
                v_induced_next = self._apply_aitken_acceleration(v_induced_next, v_current, v_prev)

            # 检查收敛
            if abs(v_induced_next - v_current) < 1e-6:
                converged = True
                break

            # 更新值
            v_prev = v_current
            v_current = v_induced_next
            v_induced_y = v_induced_next

        # 更新历史记录
        element_key = f"r_{r:.3f}"
        self._update_velocity_history(element_key, v_induced_y)

        return np.array([0.0, v_induced_y, 0.0])  # [v_axial, v_tangential, v_radial]

    def _validate_initial_guess(self, guess: float, theta: float, r: float) -> float:
        """验证初值猜测的物理合理性"""
        # 物理限制检查
        V_inf_mag = np.linalg.norm(self.V_inf)
        max_reasonable_velocity = 2.0 * V_inf_mag if V_inf_mag > 1e-6 else 10.0
        min_reasonable_velocity = -0.5 * V_inf_mag if V_inf_mag > 1e-6 else -5.0

        validated_guess = np.clip(guess, min_reasonable_velocity, max_reasonable_velocity)

        # 检查是否会导致非物理的攻角
        alpha_test = self._calculate_effective_aoa_with_induced(theta, r, validated_guess)
        alpha_deg = np.degrees(alpha_test)

        if abs(alpha_deg) > 60:  # 攻角过大，调整初值
            # 二分法寻找合理的初值
            v_low = 0.0
            v_high = validated_guess

            for _ in range(10):  # 最多10次二分
                v_mid = (v_low + v_high) / 2
                alpha_mid = self._calculate_effective_aoa_with_induced(theta, r, v_mid)
                alpha_mid_deg = np.degrees(alpha_mid)

                if abs(alpha_mid_deg) < 45:
                    validated_guess = v_mid
                    break
                elif abs(alpha_mid_deg) < abs(alpha_deg):
                    validated_guess = v_mid
                    alpha_deg = alpha_mid_deg

                if alpha_mid_deg > 0:
                    v_high = v_mid
                else:
                    v_low = v_mid

        return validated_guess

    def _get_airfoil_coefficients_with_dynamic_stall(self, alpha_eff: float, r: float, theta: float) -> Tuple[float, float]:
        """
        获取包含动态失速效应的气动系数（基于原始实现）

        Args:
            alpha_eff: 有效攻角 [rad]
            r: 径向位置 [m]
            theta: 方位角 [rad]

        Returns:
            (Cl, Cd): 升力系数和阻力系数
        """
        # 如果未启用动态失速，使用静态系数
        if not self.enable_dynamic_stall or self.blade_elements is None:
            return self._get_airfoil_coefficients(alpha_eff, r)

        try:
            # 找到对应的桨叶单元
            blade_idx = self._get_blade_index_from_theta(theta)
            elem_idx = self._find_element_index(r)

            if (blade_idx < len(self.blade_elements) and
                elem_idx < len(self.blade_elements[blade_idx])):

                element = self.blade_elements[blade_idx][elem_idx]

                # 计算攻角变化率
                alpha_dot = self._calculate_alpha_dot(element, alpha_eff)

                # 计算局部速度
                V_local = self._get_relative_velocity_magnitude(theta, r, 0.0)  # 简化

                # 时间步长（从求解器配置获取）
                dt = getattr(self, 'dt', 0.001)

                # 当前时间（简化处理）
                t = getattr(self, 'current_time', 0.0)

                # 循环翼特殊参数传递
                cycloidal_params = {
                    'radial_position': r,
                    'tip_speed_ratio': self._calculate_tip_speed_ratio(r),
                    'blade_azimuth': theta
                }

                # 使用L-B模型计算非定常系数
                Cl, Cd, Cm = element.calculate_unsteady_coefficients(
                    alpha_eff, alpha_dot, V_local, dt, t, **cycloidal_params
                )

                # 记录动态失速状态（用于分析）
                self._record_dynamic_stall_state(blade_idx, elem_idx, alpha_eff, Cl, Cd, element)

                return Cl, Cd

        except Exception as e:
            print(f"   ⚠️ L-B动态失速计算失败: {e}")

        # 回退到静态系数
        return self._get_airfoil_coefficients(alpha_eff, r)

    def _calculate_tip_speed_ratio(self, r: float) -> float:
        """计算局部尖速比"""
        V_inf_mag = np.linalg.norm(self.V_inf)
        if V_inf_mag > 1e-6:
            V_tip = self.omega_rotor * r
            return V_tip / V_inf_mag
        else:
            return 3.0  # 默认值

    def _record_dynamic_stall_state(self, blade_idx: int, elem_idx: int, alpha_eff: float,
                                   Cl: float, Cd: float, element) -> None:
        """记录动态失速状态（用于分析和调试）"""
        if not hasattr(self, 'dynamic_stall_history'):
            self.dynamic_stall_history = {}

        key = f"blade_{blade_idx}_elem_{elem_idx}"
        if key not in self.dynamic_stall_history:
            self.dynamic_stall_history[key] = {
                'time': [],
                'alpha': [],
                'Cl': [],
                'Cd': [],
                'stall_severity': []
            }

        # 记录当前状态
        self.dynamic_stall_history[key]['time'].append(self.current_time)
        self.dynamic_stall_history[key]['alpha'].append(np.degrees(alpha_eff))
        self.dynamic_stall_history[key]['Cl'].append(Cl)
        self.dynamic_stall_history[key]['Cd'].append(Cd)

        # 记录失速严重程度
        if element.lb_model is not None:
            stall_severity = element.lb_model.get_stall_severity()
            self.dynamic_stall_history[key]['stall_severity'].append(stall_severity)
        else:
            self.dynamic_stall_history[key]['stall_severity'].append(0.0)

        # 限制历史长度
        max_history = 100
        for data_list in self.dynamic_stall_history[key].values():
            if len(data_list) > max_history:
                data_list.pop(0)

    def _get_blade_index_from_theta(self, theta: float) -> int:
        """从方位角计算桨叶索引"""
        # 简化实现：假设桨叶均匀分布
        blade_spacing = 2 * np.pi / self.B
        blade_idx = int((theta % (2 * np.pi)) / blade_spacing)
        return min(blade_idx, self.B - 1)

    def _calculate_alpha_dot(self, element: BladeElementWithLB, alpha_eff: float) -> float:
        """计算攻角变化率"""
        if len(element.alpha_history) < 2:
            return 0.0

        # 使用历史数据计算攻角变化率
        dt_history = element.time_history[-1] - element.time_history[-2] if len(element.time_history) >= 2 else 0.001
        alpha_dot = (alpha_eff - element.alpha_history[-1]) / dt_history if dt_history > 1e-6 else 0.0

        # 限制攻角变化率（防止数值不稳定）
        max_alpha_dot = 1000.0  # rad/s
        alpha_dot = np.clip(alpha_dot, -max_alpha_dot, max_alpha_dot)

        return alpha_dot

    def _find_element_index(self, r: float) -> int:
        """找到径向位置对应的叶素索引"""
        if self.r_positions is None:
            return 0

        # 找到最接近的径向位置索引
        distances = np.abs(self.r_positions - r)
        return np.argmin(distances)

    def _get_induction_factors(self) -> Dict[str, np.ndarray]:
        """获取诱导因子分布"""
        induction_factors = {
            'axial': np.zeros(self.n_elements),
            'tangential': np.zeros(self.n_elements),
            'radial_positions': self.r_positions.copy() if self.r_positions is not None else np.array([])
        }

        # 从速度历史中提取诱导因子
        for i, r in enumerate(self.r_positions if self.r_positions is not None else []):
            element_key = f"r_{r:.3f}"
            if element_key in self.velocity_history and self.velocity_history[element_key]:
                v_induced = self.velocity_history[element_key][-1]
                V_inf_mag = np.linalg.norm(self.V_inf)

                if V_inf_mag > 1e-6:
                    induction_factors['axial'][i] = v_induced / V_inf_mag
                else:
                    induction_factors['axial'][i] = 0.0

                # 切向诱导因子（简化）
                V_rot = self.omega_rotor * r
                if V_rot > 1e-6:
                    induction_factors['tangential'][i] = v_induced / V_rot
                else:
                    induction_factors['tangential'][i] = 0.0

        return induction_factors

    def advance_time_step(self, dt: float) -> None:
        """
        推进时间步长（支持动态失速）

        Args:
            dt: 时间步长 [s]
        """
        # 更新当前时间
        if not hasattr(self, 'current_time'):
            self.current_time = 0.0
        self.current_time += dt

        # 存储时间步长
        self.dt = dt

        # 如果启用了动态失速，更新所有桨叶单元的时间状态
        if self.enable_dynamic_stall and self.blade_elements is not None:
            for blade_elements in self.blade_elements:
                for element in blade_elements:
                    # 清理过旧的历史数据
                    max_history_time = 0.1  # 保留0.1秒的历史
                    cutoff_time = self.current_time - max_history_time

                    while (len(element.time_history) > 1 and
                           element.time_history[0] < cutoff_time):
                        element.time_history.pop(0)
                        element.alpha_history.pop(0)
                        element.coefficient_history.pop(0)

    def reset_dynamic_stall_states(self) -> None:
        """重置动态失速状态"""
        if self.enable_dynamic_stall and self.blade_elements is not None:
            print("   🔄 重置L-B动态失速状态...")

            for blade_elements in self.blade_elements:
                for element in blade_elements:
                    # 清空历史
                    element.alpha_history.clear()
                    element.time_history.clear()
                    element.coefficient_history.clear()

                    # 重置L-B模型状态
                    if element.lb_model is not None:
                        element.lb_model.reset_states()

            # 重置时间
            self.current_time = 0.0

    # ==================== 三维效应高级修正方法（基于adevice_complement4.md规范） ====================

    def _compute_nonlinear_induced_velocity(self, r: float, theta: float) -> np.ndarray:
        """
        非线性诱导速度模型（基于adevice_complement4.md规范）

        Args:
            r: 径向位置 [m]
            theta: 方位角 [rad]

        Returns:
            induced_velocity: 非线性诱导速度 [m/s]
        """
        try:
            # 基础诱导速度
            base_velocity = self._get_induced_velocity(theta, r, 0.0)

            # 非线性修正因子
            nonlinear_factors = self._compute_nonlinear_factors(r, theta)

            # 应用非线性修正
            corrected_velocity = base_velocity.copy()

            # 径向非线性效应
            radial_factor = nonlinear_factors.get('radial_factor', 1.0)
            corrected_velocity[0] *= radial_factor  # 径向分量

            # 切向非线性效应
            tangential_factor = nonlinear_factors.get('tangential_factor', 1.0)
            corrected_velocity[1] *= tangential_factor  # 切向分量

            # 轴向非线性效应
            axial_factor = nonlinear_factors.get('axial_factor', 1.0)
            corrected_velocity[2] *= axial_factor  # 轴向分量

            return corrected_velocity

        except Exception as e:
            print(f"   ⚠️ 非线性诱导速度计算失败: {e}")
            return self._get_induced_velocity(theta, r, 0.0)

    def _compute_nonlinear_factors(self, r: float, theta: float) -> Dict[str, float]:
        """计算非线性修正因子"""
        try:
            factors = {}

            # 径向位置无量纲化
            r_nondim = r / self.R_rotor

            # 径向非线性因子（基于涡丝相互作用）
            # 在桨尖和桨根附近增强非线性效应
            tip_factor = np.exp(-10 * (1 - r_nondim)**2)
            hub_factor = np.exp(-10 * r_nondim**2)
            radial_nonlinearity = 1.0 + 0.2 * (tip_factor + hub_factor)
            factors['radial_factor'] = radial_nonlinearity

            # 切向非线性因子（基于桨叶间干扰）
            blade_interaction = self._compute_blade_interaction_factor(theta)
            tangential_nonlinearity = 1.0 + 0.1 * blade_interaction
            factors['tangential_factor'] = tangential_nonlinearity

            # 轴向非线性因子（基于尾迹收缩）
            wake_contraction = self._compute_wake_contraction_factor(r_nondim)
            axial_nonlinearity = 1.0 + 0.15 * wake_contraction
            factors['axial_factor'] = axial_nonlinearity

            return factors

        except Exception as e:
            print(f"   ⚠️ 非线性因子计算失败: {e}")
            return {'radial_factor': 1.0, 'tangential_factor': 1.0, 'axial_factor': 1.0}

    def _compute_blade_interaction_factor(self, theta: float) -> float:
        """计算桨叶间干扰因子"""
        # 基于方位角的桨叶间干扰
        blade_spacing = 2 * np.pi / self.B

        # 计算到最近桨叶的角度距离
        nearest_blade_angle = (theta % blade_spacing) / blade_spacing
        if nearest_blade_angle > 0.5:
            nearest_blade_angle = 1.0 - nearest_blade_angle

        # 干扰强度（在桨叶附近最强）
        interaction_strength = np.exp(-10 * nearest_blade_angle)

        return interaction_strength

    def _compute_wake_contraction_factor(self, r_nondim: float) -> float:
        """计算尾迹收缩因子"""
        # 尾迹收缩在中径处最强
        contraction_strength = 4 * r_nondim * (1 - r_nondim)  # 在r=0.5处最大

        return contraction_strength

    def _apply_blade_interaction_effects(self, blade_loads: List) -> List:
        """
        桨叶间干扰效应（基于adevice_complement4.md规范）

        Args:
            blade_loads: 各桨叶载荷列表

        Returns:
            corrected_loads: 修正后的桨叶载荷
        """
        try:
            if len(blade_loads) != self.B:
                print(f"   ⚠️ 桨叶载荷数量不匹配: {len(blade_loads)} vs {self.B}")
                return blade_loads

            corrected_loads = []

            for i in range(self.B):
                current_load = blade_loads[i].copy()

                # 计算相邻桨叶的影响
                for j in range(self.B):
                    if i != j:
                        # 计算桨叶间的相对位置
                        angular_separation = 2 * np.pi * abs(i - j) / self.B

                        # 干扰强度（距离越近影响越大）
                        interaction_strength = self._compute_interaction_strength(angular_separation)

                        # 应用干扰效应
                        if interaction_strength > 0.01:  # 只考虑显著影响
                            interference_load = self._compute_interference_load(
                                blade_loads[j], angular_separation, interaction_strength
                            )
                            current_load += interference_load

                corrected_loads.append(current_load)

            return corrected_loads

        except Exception as e:
            print(f"   ⚠️ 桨叶间干扰效应计算失败: {e}")
            return blade_loads

    def _compute_interaction_strength(self, angular_separation: float) -> float:
        """计算桨叶间干扰强度"""
        # 基于角度分离的干扰强度模型
        # 相邻桨叶影响最强，随角度距离衰减

        # 归一化角度分离（0-1范围）
        normalized_separation = angular_separation / np.pi

        # 指数衰减模型
        interaction_strength = 0.2 * np.exp(-2 * normalized_separation)

        return interaction_strength

    def _compute_interference_load(self, source_load: np.ndarray,
                                 angular_separation: float,
                                 interaction_strength: float) -> np.ndarray:
        """计算干扰载荷"""
        # 简化的干扰载荷模型
        # 干扰载荷与源载荷成正比，但有相位延迟

        # 相位延迟（基于角度分离）
        phase_delay = angular_separation / (2 * np.pi)

        # 干扰载荷幅度
        interference_amplitude = interaction_strength * 0.1  # 10%的最大干扰

        # 计算干扰载荷（简化为源载荷的一个分数）
        interference_load = interference_amplitude * source_load * np.cos(phase_delay * np.pi)

        return interference_load

    def _compute_radial_flow_coriolis_effects(self, velocity_field: np.ndarray) -> np.ndarray:
        """
        径向流动和Coriolis效应（基于adevice_complement4.md规范）

        Args:
            velocity_field: 速度场 [n_points, 3]

        Returns:
            corrected_velocity_field: 修正后的速度场
        """
        try:
            corrected_field = velocity_field.copy()

            # 旋转角速度
            omega = getattr(self, 'rotor_angular_velocity', 100.0)  # rad/s

            for i, velocity in enumerate(velocity_field):
                # 径向位置估计（简化）
                r_pos = self.r_positions[min(i, len(self.r_positions) - 1)]

                # Coriolis力效应
                coriolis_acceleration = self._compute_coriolis_acceleration(velocity, omega, r_pos)

                # 离心力效应
                centrifugal_acceleration = self._compute_centrifugal_acceleration(omega, r_pos)

                # 总修正（转换为速度修正）
                dt_effective = 0.001  # 有效时间尺度
                velocity_correction = (coriolis_acceleration + centrifugal_acceleration) * dt_effective

                corrected_field[i] += velocity_correction

            return corrected_field

        except Exception as e:
            print(f"   ⚠️ Coriolis效应计算失败: {e}")
            return velocity_field

    def _compute_coriolis_acceleration(self, velocity: np.ndarray, omega: float, r_pos: float) -> np.ndarray:
        """计算Coriolis加速度"""
        # Coriolis加速度: a_cor = -2 * Ω × v
        # 假设旋转轴为z轴
        omega_vector = np.array([0, 0, omega])

        # 叉积计算
        coriolis_acc = -2 * np.cross(omega_vector, velocity)

        return coriolis_acc

    def _compute_centrifugal_acceleration(self, omega: float, r_pos: float) -> np.ndarray:
        """计算离心加速度"""
        # 离心加速度: a_cf = Ω² * r (径向向外)
        centrifugal_magnitude = omega**2 * r_pos

        # 假设径向方向为x方向（简化）
        centrifugal_acc = np.array([centrifugal_magnitude, 0, 0])

        return centrifugal_acc

    def _get_adaptive_relaxation_factor(self, sub_iter: int, main_iter: int) -> float:
        """获取自适应松弛因子"""
        if not self.use_adaptive_relaxation:
            return self.relaxation_factor

        # 基础松弛因子
        base_factor = self.relaxation_factor

        # 根据迭代次数调整
        if main_iter < 5:
            factor = base_factor * 0.5
        elif main_iter < 20:
            factor = base_factor * (0.5 + 0.5 * (main_iter - 5) / 15)
        else:
            factor = base_factor * 1.2

        # 根据子迭代次数微调
        if sub_iter > 10:
            factor *= 0.8  # 收敛困难时减小松弛因子

        return np.clip(factor, 0.1, 0.9)

    def _apply_aitken_acceleration(self, v_next: float, v_current: float, v_prev: float) -> float:
        """应用Aitken's Delta-Squared加速方法"""
        # Aitken's delta-squared process
        delta1 = v_next - v_current
        delta2 = v_current - v_prev
        delta3 = delta1 - delta2

        # 防止除零
        if abs(delta3) < 1e-12:
            relaxation_factor = 0.5
            return v_current * (1 - relaxation_factor) + v_next * relaxation_factor

        # Aitken加速公式
        aitken_correction = (delta1 * delta1) / delta3
        v_accelerated = v_next - aitken_correction

        # 安全检查：确保加速后的值在合理范围内
        max_change = 2.0 * abs(v_next - v_current)
        if abs(v_accelerated - v_current) > max_change:
            sign_change = np.sign(v_accelerated - v_current)
            v_accelerated = v_current + sign_change * max_change

        return v_accelerated

    def _calculate_effective_aoa(self, theta: float, r: float, v_induced: np.ndarray) -> float:
        """计算有效攻角"""
        return self._calculate_effective_aoa_with_induced(theta, r, v_induced[1])

    def _calculate_effective_aoa_with_induced(self, theta: float, r: float, v_induced_y: float) -> float:
        """计算包含诱导速度的有效攻角 - 修正版本"""
        # 使用正确的直升机旋翼攻角计算公式
        # alpha_eff = alpha_geom - phi
        # 其中 phi 是流入角，alpha_geom 是几何攻角

        # 计算切向速度
        v_tangential = self.omega_rotor * r

        # 计算流入角 (诱导速度向下为正)
        phi = np.arctan2(abs(v_induced_y), v_tangential)

        # 计算几何攻角（总距 + 扭转角 + 周期变距）
        r_index = self._get_radial_index(r)
        twist_angle = self.twist_distribution[r_index] if r_index < len(self.twist_distribution) else 0.0

        # 几何攻角 = 总距 + 扭转角 + 周期变距效应
        alpha_geom = self.collective_pitch + twist_angle + self.cyclic_pitch * np.sin(theta)

        # 有效攻角 = 几何攻角 - 流入角
        alpha_effective = alpha_geom - phi

        return alpha_effective

    def _get_radial_index(self, r: float) -> int:
        """获取径向位置对应的索引"""
        # 线性插值找到最近的索引
        if r <= self.r_positions[0]:
            return 0
        elif r >= self.r_positions[-1]:
            return len(self.r_positions) - 1
        else:
            # 找到最近的索引
            distances = np.abs(self.r_positions - r)
            return np.argmin(distances)

    def _get_airfoil_coefficients(self, alpha: float, r: float) -> Tuple[float, float]:
        """获取翼型气动系数 - 集成真实翼型数据库"""

        # 如果有真实翼型数据库，优先使用
        if (self.use_real_airfoil_data and
            self.airfoil_database is not None and
            self.airfoil_name in self.airfoil_database.get_available_airfoils()):

            try:
                # 计算局部雷诺数
                reynolds = self._calculate_local_reynolds_number(r)

                # 从翼型数据库获取系数
                cl, cd, cm = self.airfoil_database.get_coefficients(
                    self.airfoil_name, reynolds, np.degrees(alpha)
                )

                return cl, cd

            except Exception as e:
                # 如果数据库查询失败，回退到理论模型
                pass

        # 回退到改进的理论模型（基于NACA 0012实验数据拟合）
        alpha_deg = np.degrees(alpha)

        # 升力系数计算（基于实验数据拟合）
        if abs(alpha_deg) <= 12.0:  # 线性范围
            Cl_alpha = 5.7   # 修正值：考虑有限展弦比和实验数据
            alpha_0 = 0.0    # NACA 0012对称翼型零升攻角
            Cl = Cl_alpha * np.radians(alpha_deg - alpha_0)
        elif abs(alpha_deg) <= 20.0:  # 失速过渡区
            # 使用三次多项式拟合失速区域
            if alpha_deg > 0:
                Cl = 1.4 - 0.02 * (alpha_deg - 12)**2
            else:
                Cl = -1.4 + 0.02 * (alpha_deg + 12)**2
        else:  # 深失速区域
            if alpha_deg > 0:
                Cl = 0.8 - 0.01 * (alpha_deg - 20)
            else:
                Cl = -0.8 + 0.01 * (alpha_deg + 20)

        # 阻力系数计算（基于NACA 0012实验数据）
        Cd_0 = 0.0082  # NACA 0012零升阻力系数（实验值）

        # 诱导阻力（基于升力系数）
        Cd_induced = Cl**2 / (np.pi * 6.5)  # 考虑有限展弦比效应

        # 型阻随攻角变化（基于实验数据）
        if abs(alpha_deg) <= 12.0:
            Cd_profile = Cd_0 * (1 + 0.1 * (alpha_deg/12)**2)
        else:
            # 失速区域阻力急剧增加
            Cd_profile = Cd_0 * (1 + 0.5 * ((abs(alpha_deg) - 12)/8)**2)

        Cd = Cd_profile + Cd_induced

        # 确保阻力系数的物理合理性
        Cd = max(Cd, 0.008)  # 最小阻力系数
        Cd = min(Cd, 2.0)    # 最大阻力系数

        return Cl, Cd

    def _calculate_local_reynolds_number(self, r: float) -> float:
        """计算局部雷诺数"""
        # 计算局部速度
        local_velocity = self.omega_rotor * r
        if hasattr(self, 'V_inf') and self.V_inf is not None:
            V_inf_mag = np.linalg.norm(self.V_inf)
            local_velocity = np.sqrt(local_velocity**2 + V_inf_mag**2)

        # 计算局部弦长
        chord = self._get_local_chord(r)

        # 雷诺数计算 (Re = ρVc/μ)
        kinematic_viscosity = 1.81e-5 / self.rho  # 运动粘度 [m²/s]
        reynolds = local_velocity * chord / kinematic_viscosity

        # 限制在合理范围内
        reynolds = max(50000, min(reynolds, 1000000))

        return reynolds

    def _apply_physical_corrections(self, r: float, alpha: float, Cl: float, Cd: float) -> Tuple[float, float]:
        """应用物理修正"""
        Cl_corrected = Cl
        Cd_corrected = Cd

        # 叶尖损失修正
        if self.enable_tip_loss_correction:
            tip_factor = self._tip_loss_correction(r)
            Cl_corrected *= tip_factor

        # 桂毂损失修正
        if self.enable_root_loss_correction:
            root_factor = self._root_loss_correction(r)
            Cl_corrected *= root_factor

        # 3D旋转效应修正
        if self.enable_3d_rotational_effects:
            rotation_factor = self._rotational_effects_correction(r, alpha)
            Cl_corrected *= rotation_factor

        # 压缩性修正
        if self.enable_compressibility_correction:
            V_rel = self._get_relative_velocity_magnitude(0, r, 0)  # 简化
            mach = V_rel / 343.0  # 假设声速343 m/s
            if mach < 0.8:
                comp_factor = 1.0 / np.sqrt(1 - mach**2)
                Cl_corrected *= comp_factor

        # 增强的3D修正（基于原始实现）
        if (self.enable_snel_stall_delay or self.enable_du_selig_radial_flow or
            self.enable_cycloidal_3d_corrections):
            Cl_corrected, Cd_corrected = self._apply_enhanced_3d_corrections(
                Cl_corrected, Cd_corrected, alpha, r
            )

        return Cl_corrected, Cd_corrected

    def _tip_loss_correction(self, r: float) -> float:
        """叶尖损失修正（增强Prandtl模型）"""
        if r >= self.R_rotor:
            return 0.0

        # 标准Prandtl叶尖损失
        f = self.B * (self.R_rotor - r) / (2 * r)
        F_prandtl = (2 / np.pi) * np.arccos(np.exp(-f))

        # 增强修正：在外侧半径增加额外损失
        r_ratio = r / self.R_rotor
        if r_ratio > 0.9:
            # 在90%半径以外增加额外损失
            additional_loss = 0.8 * (r_ratio - 0.9) / 0.1
            F_enhanced = F_prandtl * (1.0 - additional_loss)
        else:
            F_enhanced = F_prandtl

        return max(F_enhanced, 0.01)  # 限制最小值

    def _root_loss_correction(self, r: float) -> float:
        """桂毂损失修正"""
        if r <= self.R_hub:
            return 0.0

        f_hub = self.B * (r - self.R_hub) / (2 * self.R_hub)
        F_hub = (2 / np.pi) * np.arccos(np.exp(-f_hub))

        return max(F_hub, 0.01)

    def _rotational_effects_correction(self, r: float, alpha: float) -> float:
        """3D旋转效应修正"""
        # 简化的旋转效应模型
        # 基于Snel等人的研究

        # 计算局部雷诺数和马赫数
        V_rel = self._get_relative_velocity_magnitude(0, r, 0)
        chord_local = self._get_local_chord(r)
        Re_local = self.rho * V_rel * chord_local / 1.81e-5  # 假设动力粘度

        # 旋转效应主要在内侧半径影响较大
        r_R = r / self.R_rotor

        if r_R < 0.7:  # 内侧区域
            # 旋转效应增强升力
            rotation_factor = 1.0 + 0.2 * (0.7 - r_R) * np.sin(2 * alpha)
        else:
            rotation_factor = 1.0

        return max(rotation_factor, 0.5)  # 限制范围

    def _get_local_chord(self, r: float) -> float:
        """获取局部弦长"""
        r_index = self._get_radial_index(r)
        return self.chord_distribution[r_index] if r_index < len(self.chord_distribution) else self.c

    def _get_relative_velocity_magnitude(self, theta: float, r: float, v_induced_y: float) -> float:
        """计算相对速度大小"""
        # 桨叶速度
        V_blade_x = self.omega_rotor * r * np.cos(theta)
        V_blade_y = self.omega_rotor * r * np.sin(theta)

        # 相对速度
        V_rel_x = self.V_inf_x - V_blade_x
        V_rel_y = self.V_inf_y - V_blade_y - v_induced_y

        return np.sqrt(V_rel_x**2 + V_rel_y**2)

    def _compute_induced_velocity_from_loads(self, Cl: float, Cd: float, V_rel: float, r: float) -> float:
        """基于载荷计算诱导速度"""
        # 简化的动量理论关系
        # 这是BEMT的核心：通过载荷反推诱导速度

        chord_local = self._get_local_chord(r)
        dA = chord_local * (self.R_rotor - self.R_hub) / self.n_elements  # 叶素面积

        # 升力
        dL = 0.5 * self.rho * V_rel**2 * Cl * dA

        # 基于动量理论的诱导速度
        # dT = dL * B = rho * A * v_induced * (V_inf + v_induced)
        # 简化求解：v_induced ≈ dT / (2 * rho * A * V_inf)

        dT = dL * self.B  # 总推力增量
        A_annulus = 2 * np.pi * r * (self.R_rotor - self.R_hub) / self.n_elements  # 环形面积

        V_inf_mag = np.linalg.norm(self.V_inf)
        if V_inf_mag > 1e-6:
            v_induced_new = dT / (2 * self.rho * A_annulus * V_inf_mag)
        else:
            # 悬停情况
            if dT > 0 and A_annulus > 0:
                v_induced_new = np.sqrt(dT / (2 * self.rho * A_annulus))
            else:
                v_induced_new = 0.0

        return v_induced_new

    def _calculate_element_loads(self, r: float, alpha: float, Cl: float, Cd: float,
                               v_induced: np.ndarray, blade_idx: int = 0, elem_idx: int = 0) -> Tuple[np.ndarray, np.ndarray, float]:
        """计算叶素载荷 - 增强版本"""
        # 获取局部参数
        chord_local = self._get_local_chord(r)
        dr = (self.R_rotor - self.R_hub) / self.n_elements

        # 计算相对速度
        V_rel = self._get_relative_velocity_magnitude(0, r, v_induced[1])  # 简化theta=0

        # 动压
        q = 0.5 * self.rho * V_rel**2

        # 叶素面积
        dA = chord_local * dr

        # 升力和阻力
        dL = Cl * q * dA
        dD = Cd * q * dA

        # 计算流入角用于力的坐标转换
        v_tangential = self.omega_rotor * r
        v_induced_magnitude = abs(v_induced[1]) if len(v_induced) > 1 else 0.0
        phi = np.arctan2(v_induced_magnitude, v_tangential)

        # 力的分量（在桨叶坐标系中）
        # 升力垂直于相对速度方向，阻力平行于相对速度方向
        force_lift = dL  # 升力
        force_drag = dD  # 阻力

        # 转换到旋翼坐标系（考虑流入角）
        # 推力 = 升力*cos(phi) - 阻力*sin(phi)
        # 扭矩力 = 升力*sin(phi) + 阻力*cos(phi)
        force_thrust = force_lift * np.cos(phi) - force_drag * np.sin(phi)  # 推力（向上）
        force_torque = force_lift * np.sin(phi) + force_drag * np.cos(phi)  # 扭矩力（切向）
        force_axial = 0.0  # 轴向力（简化）

        # 总力向量（旋翼坐标系）
        force = np.array([
            force_torque,   # x方向（切向，产生扭矩）
            force_thrust,   # y方向（推力，向上）
            force_axial     # z方向（轴向）
        ])

        # 力矩（绕旋翼中心）
        moment_pitching = dL * chord_local * 0.25  # 俯仰力矩（简化）
        moment = np.array([
            0.0,              # 滚转力矩
            moment_pitching,  # 俯仰力矩
            force_torque * r  # 扭矩（使用修正后的变量名）
        ])

        # 环量
        circulation = Cl * V_rel * chord_local / (2 * np.pi)

        # 记录详细载荷历史（用于声学分析）
        if self.enable_detailed_loads_history:
            self._record_element_loads(
                self.current_time, blade_idx, elem_idx,
                force_thrust, force_torque, force_axial, moment_pitching
            )

        # 更新桨叶状态（用于桨叶间干扰）
        if self.use_blade_interaction:
            self._update_blade_state(blade_idx, circulation, force)

        return force, moment, circulation

    def _update_induced_velocity_guess(self, v_guess: np.ndarray, element_forces: List[np.ndarray],
                                     iteration: int) -> np.ndarray:
        """更新诱导速度猜测"""
        # 基于载荷更新诱导速度猜测
        v_new_guess = v_guess.copy()

        for i, (r, force) in enumerate(zip(self.r_positions, element_forces)):
            # 基于推力更新轴向诱导速度
            thrust_element = force[2]  # z方向力

            # 简化的动量理论更新
            A_annulus = 2 * np.pi * r * (self.R_rotor - self.R_hub) / self.n_elements

            if A_annulus > 1e-6:
                v_induced_update = thrust_element / (2 * self.rho * A_annulus *
                                                   (np.linalg.norm(self.V_inf) + 1e-6))

                # 应用松弛
                relaxation = self._get_adaptive_relaxation_factor(0, iteration)
                v_new_guess[i, 1] = (1 - relaxation) * v_guess[i, 1] + relaxation * v_induced_update

        return v_new_guess

    def _update_velocity_history(self, element_key: str, velocity: float) -> None:
        """更新速度历史记录"""
        if element_key not in self.velocity_history:
            self.velocity_history[element_key] = []

        self.velocity_history[element_key].append(velocity)

        # 限制历史记录长度
        max_history = 10
        if len(self.velocity_history[element_key]) > max_history:
            self.velocity_history[element_key].pop(0)

    def _solve_static_wing_mode(self) -> Tuple[np.ndarray, np.ndarray, bool, int]:
        """静态翼模式求解（当转速为零时）"""
        total_forces = np.zeros(3)
        total_moments = np.zeros(3)

        # 简化的静态翼计算
        for i, r in enumerate(self.r_positions):
            # 来流攻角
            V_inf_mag = np.linalg.norm(self.V_inf)
            if V_inf_mag > 1e-6:
                alpha = np.arctan2(self.V_inf[2], self.V_inf[0])  # 简化
            else:
                alpha = 0.0

            # 获取气动系数
            Cl, Cd = self._get_airfoil_coefficients(alpha, r)

            # 计算载荷
            chord_local = self._get_local_chord(r)
            dr = (self.R_rotor - self.R_hub) / self.n_elements
            dA = chord_local * dr
            q = 0.5 * self.rho * V_inf_mag**2

            dL = Cl * q * dA * self.B
            dD = Cd * q * dA * self.B

            total_forces[0] -= dD
            total_forces[2] += dL
            total_moments[2] += dD * r

        return total_forces, total_moments, True, 1

    def _solve_static_wing_mode_element(self, theta: float, r: float) -> np.ndarray:
        """静态翼模式单个叶素求解"""
        # 静态翼模式下没有诱导速度
        return np.array([0.0, 0.0, 0.0])

    def _compute_forces_3d(self) -> None:
        """计算3D力分布（用于声学耦合）"""
        # 简化实现：存储每个叶素的力
        self.forces_3d = np.zeros((self.n_elements, 3))

        for i, r in enumerate(self.r_positions):
            # 使用最新的计算结果
            element_key = f"r_{r:.3f}"
            if element_key in self.velocity_history:
                # 重新计算该叶素的力
                v_induced = np.array([0.0, self.velocity_history[element_key][-1], 0.0])
                alpha_eff = self._calculate_effective_aoa(0, r, v_induced)  # 简化theta=0
                Cl, Cd = self._get_airfoil_coefficients(alpha_eff, r)
                Cl_corr, Cd_corr = self._apply_physical_corrections(r, alpha_eff, Cl, Cd)
                force, _, _ = self._calculate_element_loads(r, alpha_eff, Cl_corr, Cd_corr, v_induced)
                self.forces_3d[i] = force

    def _compute_pressure_distribution(self) -> np.ndarray:
        """计算压力分布（简化）"""
        pressure_dist = np.zeros(self.n_elements)

        for i, r in enumerate(self.r_positions):
            # 基于局部动压的简化压力
            V_rel = self._get_relative_velocity_magnitude(0, r, 0)  # 简化
            q = 0.5 * self.rho * V_rel**2
            pressure_dist[i] = 101325.0 + q  # 大气压 + 动压

        return pressure_dist

    def _compute_velocity_field(self) -> np.ndarray:
        """计算速度场（简化）"""
        velocity_field = np.zeros((self.n_elements, 3))

        for i, r in enumerate(self.r_positions):
            # 局部速度（旋转 + 来流）
            V_rot = self.omega_rotor * r
            velocity_field[i] = [V_rot, 0.0, 0.0]  # 简化

        return velocity_field

    def get_blade_positions(self) -> np.ndarray:
        """获取桨叶位置"""
        positions = np.zeros((self.B, 3))

        for i in range(self.B):
            angle = self.omega_rotor * self.current_time + i * 2 * np.pi / self.B
            positions[i, 0] = self.R_rotor * np.cos(angle)
            positions[i, 1] = self.R_rotor * np.sin(angle)
            positions[i, 2] = 0.0

        return positions

    def get_circulation_distribution(self) -> np.ndarray:
        """获取环量分布"""
        circulation = np.zeros(self.n_elements)

        for i, r in enumerate(self.r_positions):
            element_key = f"r_{r:.3f}"
            if element_key in self.velocity_history:
                # 基于最新的诱导速度计算环量
                v_induced = np.array([0.0, self.velocity_history[element_key][-1], 0.0])
                alpha_eff = self._calculate_effective_aoa(0, r, v_induced)
                Cl, _ = self._get_airfoil_coefficients(alpha_eff, r)
                V_rel = self._get_relative_velocity_magnitude(0, r, v_induced[1])
                chord_local = self._get_local_chord(r)
                circulation[i] = Cl * V_rel * chord_local / (2 * np.pi)

        return circulation

    # ==================== 原始算法完全复刻 ====================

    def _solve_induced_velocity_iterative(self, blade_idx: int, elem_idx: int,
                                         theta: float, r: float) -> Tuple[np.ndarray, bool]:
        """
        诱导速度迭代求解 - 完全复刻原始算法

        基于原始 cycloidal_rotor_suite 的完整实现，包括：
        - Aitken加速算法
        - 自适应松弛因子
        - 振荡检测和处理
        - 数值稳定性保障

        Args:
            blade_idx: 桨叶索引
            elem_idx: 叶素索引
            theta: 方位角
            r: 径向位置

        Returns:
            (v_induced, converged): 诱导速度向量和收敛标志
        """
        # 计算扫掠面积
        swept_area = np.pi * self.R_rotor**2
        if abs(swept_area) < 1e-12:
            swept_area = max(swept_area, 1e-12)

        # 智能初值猜测
        element_key = f"{blade_idx}_{elem_idx}"
        if self.enable_smart_initial_guess:
            v_induced_y = self._get_smart_initial_guess(blade_idx, elem_idx, theta, r)
        else:
            v_induced_y = 0.1

        # 初始化历史记录
        if not hasattr(self, "previous_induced_velocities"):
            self.previous_induced_velocities = {}

        v_prev = v_induced_y
        v_pprev = v_induced_y
        convergence_history = []

        # 松弛因子参数（与原始代码完全一致）
        base_relaxation = 0.3
        min_relaxation = 0.1
        max_relaxation = 0.7
        aitken_counter = 0
        aitken_max = 2

        # 安全迭代限制
        max_iter_safe = min(self.max_iterations, 100)

        for iteration in range(max_iter_safe):
            # 计算有效攻角
            alpha_eff = self._calculate_effective_aoa_with_induced(theta, r, v_induced_y)
            alpha_eff_deg = np.degrees(alpha_eff)
            is_extreme_aoa = abs(alpha_eff_deg) > 30.0

            # 获取翼型系数
            Cl, Cd = self._get_airfoil_coefficients(alpha_eff, r)

            # 计算相对速度
            v_rel = self._get_relative_velocity_magnitude(theta, r, v_induced_y)
            q_dyn = 0.5 * self.rho * v_rel**2

            # 计算叶素参数
            dr = 2 * self.R_rotor / self.n_elements
            dA = self.c * dr
            dL = Cl * q_dyn * dA

            # 动量理论计算新诱导速度
            if abs(dL) < 1e-12 or swept_area < 1e-12:
                v_next_guess = 0.0
            else:
                v_magnitude = np.sqrt(abs(dL) / (2 * self.rho * swept_area))
                max_induced_velocity = 0.5 * self.omega_rotor * self.R_rotor
                v_magnitude = min(v_magnitude, max_induced_velocity)
                v_next_guess = np.sign(dL) * v_magnitude

            # 自适应松弛因子（与原始代码逻辑完全一致）
            if is_extreme_aoa:
                relaxation_factor = max(min_relaxation, 0.05)
            elif len(convergence_history) >= 3:
                recent_residuals = convergence_history[-3:]
                if recent_residuals[-1] < recent_residuals[-2]:
                    relaxation_factor = min(max_relaxation, base_relaxation * 1.2)
                    relaxation_factor = min(relaxation_factor, 0.8)
                else:
                    relaxation_factor = max(min_relaxation, base_relaxation * 0.8)
                    relaxation_factor = max(relaxation_factor, 0.05)
            else:
                relaxation_factor = np.clip(base_relaxation, 0.05, 0.8)

            # Aitken加速（与原始代码完全一致）
            if iteration >= aitken_max and not is_extreme_aoa:
                try:
                    # 振荡检测
                    is_oscillating = (
                        len(convergence_history) >= 4
                        and convergence_history[-1] > convergence_history[-2]
                        and convergence_history[-2] < convergence_history[-3]
                        and convergence_history[-3] > convergence_history[-4]
                    )

                    if is_oscillating:
                        # 振荡时使用平均值
                        v_new = 0.5 * (v_induced_y + v_next_guess)
                    else:
                        # 应用Aitken加速
                        v_aitken = self._apply_aitken_acceleration(v_next_guess, v_induced_y, v_prev)
                        if abs(v_aitken - v_induced_y) < 2 * abs(v_next_guess - v_induced_y):
                            v_new = v_aitken
                            aitken_counter += 1
                        else:
                            v_new = v_induced_y * (1 - relaxation_factor) + v_next_guess * relaxation_factor
                except Exception:
                    v_new = v_induced_y * (1 - relaxation_factor) + v_next_guess * relaxation_factor
            else:
                v_new = v_induced_y * (1 - relaxation_factor) + v_next_guess * relaxation_factor

            # 计算残差
            residual = abs(v_new - v_induced_y)
            convergence_history.append(residual)

            # 自适应容差
            effective_tolerance = self._calculate_adaptive_tolerance(
                iteration, convergence_history, is_extreme_aoa, alpha_eff
            )

            # 收敛检查
            if residual < effective_tolerance:
                self.previous_induced_velocities[element_key] = v_new
                # 返回3D向量格式
                return np.array([v_new, 0.0, 0.0]), True

            # 更新历史
            v_pprev = v_prev
            v_prev = v_induced_y
            v_induced_y = v_new

            # 速度限制
            max_velocity_limit = 100.0
            if abs(v_induced_y) > max_velocity_limit:
                v_induced_y = np.clip(v_induced_y, -max_velocity_limit, max_velocity_limit)

        # 未收敛时的处理
        self.previous_induced_velocities[element_key] = v_induced_y
        return np.array([v_induced_y, 0.0, 0.0]), False

    def _calculate_element_loads_original(self, r: float, alpha: float, Cl: float,
                                        Cd: float, v_induced: np.ndarray) -> Tuple[np.ndarray, np.ndarray, float]:
        """
        计算叶素载荷 - 完全复刻原始算法

        基于原始代码的精确实现，包括：
        - 精确的坐标变换
        - 力分量计算
        - 数值稳定性处理

        Args:
            r: 径向位置
            alpha: 攻角
            Cl, Cd: 气动系数
            v_induced: 诱导速度向量 [v_axial, v_tangential, v_radial]

        Returns:
            force, moment, circulation
        """
        # 计算速度分量（与原始代码完全一致）
        v_tangential = self.omega_rotor * r  # 切向速度
        v_axial = v_induced[0]  # 轴向诱导速度
        v_radial = v_induced[2] if len(v_induced) > 2 else 0.0  # 径向诱导速度

        # 计算总相对速度
        v_rel_total = np.sqrt(v_tangential**2 + v_axial**2 + v_radial**2)

        # 计算流入角
        phi = np.arctan2(v_axial, v_tangential)

        # 计算动压
        q_dyn = 0.5 * self.rho * v_rel_total**2

        # 计算叶素面积
        dr = 2 * self.R_rotor / self.n_elements
        dA = self.c * dr

        # 计算升力和阻力
        dL = Cl * q_dyn * dA
        dD = Cd * q_dyn * dA

        # 坐标变换（与原始代码完全一致）
        cos_phi = np.cos(phi)
        sin_phi = np.sin(phi)

        # 力分量计算（原始代码的精确公式）
        force_axial = dL * cos_phi - dD * sin_phi  # 轴向力
        force_tangential = -dL * sin_phi - dD * cos_phi  # 切向力
        force_radial = 0.0  # 径向力（简化为0）

        # 数值稳定性处理（与原始代码一致）
        if np.any(force_axial < 0) and np.any(alpha > 0):
            force_axial = np.abs(force_axial)

        # 安全标量转换函数（与原始代码一致）
        def safe_scalar_conversion(value, default=0.0):
            try:
                arr = np.asarray(value)
                if arr.ndim == 0:
                    return float(arr)
                elif arr.size == 1:
                    return float(arr.flatten()[0])
                else:
                    return float(np.mean(arr))
            except (ValueError, TypeError):
                return default

        force_tangential = safe_scalar_conversion(force_tangential, 0.0)
        force_radial = safe_scalar_conversion(force_radial, 0.0)
        force_axial = safe_scalar_conversion(force_axial, 100.0)  # 默认推力
        force = np.array([force_tangential, force_radial, force_axial])

        # 计算力矩（与原始代码一致）
        moment_z = force_tangential * r  # 扭矩
        moment = np.array([0.0, 0.0, moment_z])

        # 计算环量（Kutta-Joukowski定理）
        circulation = Cl * v_rel_total * self.c / 2
        circulation = safe_scalar_conversion(circulation, 0.0)

        return force, moment, circulation

    def _calculate_adaptive_tolerance(self, iteration: int, convergence_history: list,
                                    is_extreme_aoa: bool, alpha_eff: float) -> float:
        """
        计算自适应容差 - 与原始代码一致

        Args:
            iteration: 当前迭代次数
            convergence_history: 收敛历史
            is_extreme_aoa: 是否为极端攻角
            alpha_eff: 有效攻角

        Returns:
            effective_tolerance: 有效容差
        """
        base_tolerance = self.convergence_tolerance

        if is_extreme_aoa:
            # 极端攻角时放宽容差
            return base_tolerance * 10.0
        elif iteration < 5:
            # 初期迭代时使用较严格容差
            return base_tolerance * 0.1
        elif len(convergence_history) >= 3:
            # 根据收敛趋势调整容差
            recent_trend = convergence_history[-1] / convergence_history[-3]
            if recent_trend < 0.5:  # 快速收敛
                return base_tolerance * 0.5
            elif recent_trend > 2.0:  # 发散趋势
                return base_tolerance * 5.0

        return base_tolerance

    def _calculate_effective_aoa_with_induced(self, theta: float, r: float,
                                            v_induced: float) -> float:
        """
        计算考虑诱导速度的有效攻角 - 与原始代码一致

        Args:
            theta: 方位角
            r: 径向位置
            v_induced: 诱导速度

        Returns:
            alpha_eff: 有效攻角
        """
        # 计算切向速度
        v_tangential = self.omega_rotor * r

        # 计算流入角
        phi = np.arctan2(v_induced, v_tangential)

        # 计算几何攻角（简化）
        alpha_geom = self.collective_pitch + self.cyclic_pitch * np.sin(theta)

        # 有效攻角 = 几何攻角 - 流入角
        alpha_eff = alpha_geom - phi

        return alpha_eff

    def _get_induction_factors(self) -> Dict[str, np.ndarray]:
        """获取诱导因子"""
        induction_factors = {
            'axial': np.zeros(self.n_elements),
            'tangential': np.zeros(self.n_elements)
        }

        for i, r in enumerate(self.r_positions):
            element_key = f"r_{r:.3f}"
            if element_key in self.velocity_history:
                v_induced = self.velocity_history[element_key][-1]
                V_tip = self.omega_rotor * self.R_rotor
                if V_tip > 1e-6:
                    induction_factors['tangential'][i] = v_induced / V_tip

        return induction_factors

    # 实现基类的抽象方法
    def _setup_blade_elements(self, geometry_data: Dict[str, Any]) -> None:
        """设置叶片单元"""
        # BEMT求解器的叶片单元设置已在initialize方法中完成
        pass

    def _initialize_wake(self) -> None:
        """初始化尾迹"""
        # BEMT求解器不需要复杂的尾迹模型
        self.wake_data = {"initialized": True}

    def _compute_induced_velocity(self, blade_positions: np.ndarray) -> np.ndarray:
        """计算诱导速度"""
        # 返回当前存储的诱导速度
        induced_velocities = np.zeros((len(blade_positions), 3))

        for i, r in enumerate(self.r_positions[:len(blade_positions)]):
            element_key = f"r_{r:.3f}"
            if element_key in self.velocity_history and self.velocity_history[element_key]:
                induced_velocities[i, 1] = self.velocity_history[element_key][-1]

        return induced_velocities

    def _compute_blade_loads(self, velocity_field: np.ndarray,
                           blade_positions: np.ndarray) -> tuple:
        """计算叶片载荷"""
        # 使用BEMT的载荷计算方法
        total_forces = np.zeros(3)
        total_moments = np.zeros(3)

        for i, pos in enumerate(blade_positions):
            r = np.linalg.norm(pos[:2])  # 径向距离
            if r > self.R_hub and r <= self.R_rotor:
                # 简化的载荷计算
                v_induced = velocity_field[i] if i < len(velocity_field) else np.zeros(3)
                alpha_eff = self._calculate_effective_aoa(0, r, v_induced)
                Cl, Cd = self._get_airfoil_coefficients(alpha_eff, r)
                Cl_corr, Cd_corr = self._apply_physical_corrections(r, alpha_eff, Cl, Cd)
                force, moment, _ = self._calculate_element_loads(r, alpha_eff, Cl_corr, Cd_corr, v_induced)

                total_forces += force
                total_moments += moment

        return total_forces, total_moments

    def _update_wake(self, blade_positions: np.ndarray, velocity: np.ndarray) -> None:
        """更新尾迹"""
        # BEMT求解器不需要复杂的尾迹更新
        if self.wake_data is None:
            self.wake_data = {}

        self.wake_data["last_update_time"] = self.current_time
        self.wake_data["blade_positions"] = blade_positions.copy()

    # ==================== 智能初值猜测系统 ====================

    def _get_smart_initial_guess(self, blade_idx: int, elem_idx: int, theta: float, r: float) -> float:
        """
        智能初值猜测系统 - 基于原始实现

        结合历史数据预测、物理基础猜测和空间插值的综合初值估计

        Args:
            blade_idx: 桨叶索引
            elem_idx: 叶素索引
            theta: 方位角 [rad]
            r: 径向位置 [m]

        Returns:
            initial_guess: 诱导速度初值猜测 [m/s]
        """
        if not self.enable_smart_initial_guess:
            return 0.0

        element_key = f"r_{r:.3f}"

        # 1. 历史数据预测
        historical_guess = self._get_historical_based_guess(element_key, theta, r)

        # 2. 物理基础猜测
        physics_guess = self._get_physics_based_guess(theta, r)

        # 3. 空间插值猜测
        spatial_guess = self._get_spatial_interpolation_guess(blade_idx, elem_idx, r)

        # 综合权重分配
        if len(self.velocity_history.get(element_key, [])) >= 3:
            # 有足够历史数据，主要依赖历史预测
            weights = [0.6, 0.3, 0.1]
        elif len(self.velocity_history.get(element_key, [])) >= 1:
            # 有部分历史数据，平衡历史和物理
            weights = [0.4, 0.4, 0.2]
        else:
            # 无历史数据，主要依赖物理和空间
            weights = [0.0, 0.7, 0.3]

        # 加权平均
        initial_guess = (weights[0] * historical_guess +
                        weights[1] * physics_guess +
                        weights[2] * spatial_guess)

        # 物理约束检查
        max_reasonable = 0.5 * self.omega_rotor * r  # 不超过叶尖速度的一半
        initial_guess = np.clip(initial_guess, -max_reasonable, max_reasonable)

        return initial_guess

    def _get_historical_based_guess(self, element_key: str, theta: float, r: float) -> float:
        """
        基于历史数据的预测 - 基于原始实现

        使用三阶预测或线性外推预测下一时间步的诱导速度
        """
        if element_key not in self.velocity_history:
            return 0.0

        history = self.velocity_history[element_key]

        if len(history) < 2:
            return history[-1] if history else 0.0

        # 三阶预测（如果有足够数据）
        if len(history) >= 3:
            v1, v2, v3 = history[-3:]
            # 三阶Adams-Bashforth预测
            predicted = 3*v3 - 3*v2 + v1
        else:
            # 线性外推
            v1, v2 = history[-2:]
            predicted = 2*v2 - v1

        return predicted

    def _get_physics_based_guess(self, theta: float, r: float) -> float:
        """
        基于物理的初值猜测 - 基于原始实现

        使用动量理论估算合理的诱导速度初值
        """
        # 计算零诱导速度下的攻角
        alpha_0 = self._calculate_effective_aoa_with_induced(theta, r, 0.0)

        # 获取翼型系数
        Cl_0, Cd_0 = self._get_airfoil_coefficients(alpha_0, r)

        # 计算相对速度
        v_rel = self._get_relative_velocity_magnitude(theta, r, 0.0)

        # 动压
        q_dyn = 0.5 * self.rho * v_rel**2

        # 叶素升力
        chord = self._get_chord(r)
        dr = 2 * self.R_rotor / self.n_elements
        dL = Cl_0 * q_dyn * chord * dr

        # 动量理论估算
        disk_area = np.pi * self.R_rotor**2

        if dL > 0 and disk_area > 1e-12 and abs(self.rho) > 1e-12:
            v_induced = np.sqrt(dL / (2 * self.rho * disk_area))
            # 限制最大值
            v_induced = min(v_induced, 0.3 * self.omega_rotor * r)
        else:
            v_induced = 0.0

        # 径向修正因子
        r_ratio = r / self.R_rotor
        radial_factor = 0.5 + 0.5 * r_ratio
        return v_induced * radial_factor

    def _get_spatial_interpolation_guess(self, blade_idx: int, elem_idx: int, r: float) -> float:
        """
        基于空间插值的猜测 - 基于原始实现

        使用相邻叶素的诱导速度进行插值估算
        """
        if self.previous_induced_velocities is None:
            return 0.0

        # 从相邻叶素插值
        if elem_idx > 0 and elem_idx < len(self.r_positions) - 1:
            # 线性插值
            v_inner = self.previous_induced_velocities.get(f"r_{self.r_positions[elem_idx-1]:.3f}", 0.0)
            v_outer = self.previous_induced_velocities.get(f"r_{self.r_positions[elem_idx+1]:.3f}", 0.0)
            return 0.5 * (v_inner + v_outer)
        elif elem_idx == 0 and len(self.r_positions) > 1:
            # 外推到根部
            return self.previous_induced_velocities.get(f"r_{self.r_positions[1]:.3f}", 0.0)
        elif elem_idx == len(self.r_positions) - 1 and len(self.r_positions) > 1:
            # 外推到叶尖
            return self.previous_induced_velocities.get(f"r_{self.r_positions[-2]:.3f}", 0.0)

        return 0.0

    # ==================== 增强物理修正模型 ====================

    def _apply_enhanced_3d_corrections(self, cl_2d: float, cd_2d: float, alpha: float, r: float) -> Tuple[float, float]:
        """
        增强的三维修正 - 基于原始实现的Snel和Du & Selig模型

        Args:
            cl_2d: 二维升力系数
            cd_2d: 二维阻力系数
            alpha: 攻角 [rad]
            r: 径向位置 [m]

        Returns:
            cl_3d, cd_3d: 修正后的三维系数
        """
        r_over_R = r / self.R_rotor
        local_omega = self.omega_rotor * r
        centrifugal_param = local_omega**2 * r / 9.81  # 无量纲离心参数

        cl_3d, cd_3d = cl_2d, cd_2d

        # Snel旋转失速延迟修正
        if self.enable_snel_stall_delay:
            cl_3d, cd_3d = self._apply_snel_stall_delay_correction(
                cl_3d, cd_3d, alpha, r_over_R, centrifugal_param
            )

        # Du & Selig径向流动效应修正
        if self.enable_du_selig_radial_flow:
            cl_3d, cd_3d = self._apply_du_selig_radial_flow_correction(
                cl_3d, cd_3d, alpha, r_over_R, centrifugal_param
            )

        # 循环翼转子特有修正
        if self.enable_cycloidal_3d_corrections:
            cl_3d, cd_3d = self._apply_cycloidal_3d_corrections(
                cl_3d, cd_3d, alpha, r_over_R
            )

        return cl_3d, cd_3d

    def _apply_snel_stall_delay_correction(self, cl_2d: float, cd_2d: float,
                                         alpha: float, r_over_R: float,
                                         centrifugal_param: float) -> Tuple[float, float]:
        """
        Snel旋转失速延迟修正 - 基于原始实现

        实现基于Snel模型的旋转失速延迟修正，显著提升高攻角区域的升力预测精度
        """
        alpha_deg = np.degrees(alpha)

        # Snel修正参数
        c1 = 1.0 + 0.1 * r_over_R  # 径向位置影响
        c2 = 0.05 * centrifugal_param  # 离心力影响

        # 失速延迟因子
        if abs(alpha_deg) > 10.0:  # 只在大攻角时应用
            stall_delay_factor = 1.0 + c1 * c2 * np.sin(alpha)
            cl_corrected = cl_2d * stall_delay_factor

            # 阻力修正（失速延迟通常减少阻力）
            cd_corrected = cd_2d * (1.0 - 0.5 * c2)
        else:
            cl_corrected = cl_2d
            cd_corrected = cd_2d

        return cl_corrected, cd_corrected

    def _apply_du_selig_radial_flow_correction(self, cl_2d: float, cd_2d: float,
                                             alpha: float, r_over_R: float,
                                             centrifugal_param: float) -> Tuple[float, float]:
        """
        Du & Selig径向流动效应修正 - 基于原始实现

        考虑旋转翼叶片上的径向流动对气动系数的影响
        """
        # 径向流动参数
        radial_flow_param = 0.02 * r_over_R * centrifugal_param

        # 升力系数修正
        cl_radial_factor = 1.0 + radial_flow_param * np.cos(alpha)
        cl_corrected = cl_2d * cl_radial_factor

        # 阻力系数修正
        cd_radial_factor = 1.0 + 0.5 * radial_flow_param * np.sin(alpha)**2
        cd_corrected = cd_2d * cd_radial_factor

        return cl_corrected, cd_corrected

    def _apply_cycloidal_3d_corrections(self, cl_3d: float, cd_3d: float,
                                      alpha: float, r_over_R: float) -> Tuple[float, float]:
        """
        循环翼转子特有的三维效应修正 - 强化版本（基于adevice_complement2.md优化）

        考虑循环翼转子的特殊物理效应：
        1. 大攻角变化（±30°）的非线性效应
        2. 低尖速比（λ<1）的强三维流动
        3. 摆线运动的非定常效应
        4. 桨叶-涡干扰效应
        """
        alpha_deg = np.degrees(alpha)

        # 1. 强化的大攻角修正（循环翼特有的±30°攻角范围）
        if abs(alpha_deg) > 15.0:
            # 大攻角非线性修正，考虑失速延迟
            stall_delay_factor = 1.0 + 0.15 * r_over_R * np.exp(-abs(alpha_deg - 15.0) / 10.0)
            large_alpha_factor = stall_delay_factor * (1.0 + 0.08 * np.sin(alpha) * r_over_R)
        else:
            large_alpha_factor = 1.0

        # 2. 低尖速比强三维流动修正（λ<1时的径向流动增强）
        tip_speed_ratio = r_over_R  # 简化的尖速比近似
        if tip_speed_ratio < 1.0:
            radial_flow_enhancement = 1.0 + 0.12 * (1.0 - tip_speed_ratio) * np.sin(alpha)**2
        else:
            radial_flow_enhancement = 1.0

        # 3. 摆线运动非定常效应（基于运动学特征）
        cycloidal_unsteady_factor = 1.0 + 0.1 * np.sin(2 * alpha) * r_over_R

        # 4. 桨叶曲率效应（循环翼的弯曲桨叶）
        curvature_factor = 1.0 - 0.03 * r_over_R * np.cos(alpha)

        # 5. 桨叶-涡干扰效应（循环翼特有的复杂尾迹）
        vortex_interaction_factor = 1.0 + 0.06 * np.sin(3 * alpha) * (1.0 - r_over_R)

        # 应用强化的循环翼修正
        cl_cycloidal = cl_3d * large_alpha_factor * radial_flow_enhancement * \
                      cycloidal_unsteady_factor * curvature_factor * vortex_interaction_factor

        # 循环翼专用阻力修正
        cycloidal_drag_factor = 1.0 + 0.08 * (alpha_deg / 30.0)**2 * r_over_R
        induced_drag_factor = 1.0 + 0.04 * np.sin(alpha)**2  # 诱导阻力增加
        cd_cycloidal = cd_3d * curvature_factor * cycloidal_drag_factor * induced_drag_factor

        return cl_cycloidal, cd_cycloidal

    # ==================== 详细载荷历史记录 ====================

    def _record_element_loads(self, time: float, blade_idx: int, elem_idx: int,
                            force_normal: float, force_tangential: float,
                            force_axial: float, moment_pitching: float) -> None:
        """
        记录叶素详细载荷历史 - 用于声学分析

        基于原始实现，为FWH声学求解器提供详细的载荷时间历史

        Args:
            time: 时间 [s]
            blade_idx: 桨叶索引
            elem_idx: 叶素索引
            force_normal: 法向力 [N]
            force_tangential: 切向力 [N]
            force_axial: 轴向力 [N]
            moment_pitching: 俯仰力矩 [N·m]
        """
        if not self.enable_detailed_loads_history:
            return

        load_record = {
            'time': time,
            'blade_idx': blade_idx,
            'elem_idx': elem_idx,
            'r': self.r_positions[elem_idx] if elem_idx < len(self.r_positions) else 0.0,
            'force_normal': force_normal,
            'force_tangential': force_tangential,
            'force_axial': force_axial,
            'moment_pitching': moment_pitching,
            'total_force_magnitude': np.sqrt(force_normal**2 + force_tangential**2 + force_axial**2)
        }

        self.detailed_loads_history.append(load_record)

        # 限制历史长度以避免内存问题
        max_history_length = 10000
        if len(self.detailed_loads_history) > max_history_length:
            self.detailed_loads_history = self.detailed_loads_history[-max_history_length//2:]

    def get_detailed_loads_history(self) -> List[Dict[str, Any]]:
        """获取详细载荷历史数据"""
        return self.detailed_loads_history.copy()

    def clear_detailed_loads_history(self) -> None:
        """清空详细载荷历史"""
        self.detailed_loads_history.clear()

    # ==================== 桨叶间干扰效应 ====================

    def _update_blade_state(self, blade_idx: int, circulation: float, force: np.ndarray) -> None:
        """
        更新桨叶状态 - 用于桨叶间干扰计算

        Args:
            blade_idx: 桨叶索引
            circulation: 环量
            force: 力向量
        """
        if not self.use_blade_interaction:
            return

        self.blade_states[blade_idx] = {
            'circulation': circulation,
            'force': force.copy(),
            'time': self.current_time
        }

    def _get_blade_interaction_correction(self, blade_idx: int, alpha_eff: float,
                                        local_velocity: float) -> Tuple[float, float]:
        """
        计算桨叶间干扰修正 - 简化实现

        Args:
            blade_idx: 当前桨叶索引
            alpha_eff: 有效攻角
            local_velocity: 局部速度

        Returns:
            corrected_velocity, corrected_alpha: 修正后的速度和攻角
        """
        if not self.use_blade_interaction or len(self.blade_states) < 2:
            return local_velocity, alpha_eff

        # 简化的桨叶间干扰模型
        interaction_factor = 0.0

        for other_blade_idx, state in self.blade_states.items():
            if other_blade_idx != blade_idx:
                # 计算桨叶间的相互影响
                circulation_ratio = state['circulation'] / (abs(state['circulation']) + 1e-6)
                interaction_factor += 0.02 * circulation_ratio  # 简化的干扰因子

        # 应用干扰修正
        corrected_velocity = local_velocity * (1.0 + interaction_factor)
        corrected_alpha = alpha_eff + 0.01 * interaction_factor  # 攻角修正

        return corrected_velocity, corrected_alpha

    def _find_element_index(self, r: float) -> int:
        """根据径向位置找到对应的叶素索引"""
        if self.r_positions is None or len(self.r_positions) == 0:
            return 0

        # 找到最接近的叶素
        distances = np.abs(self.r_positions - r)
        return int(np.argmin(distances))

    def _initialize_airfoil_database(self):
        """初始化翼型数据库"""
        try:
            from core.aerodynamics.airfoil_database import create_database_with_path
            import os

            # 获取翼型数据路径
            current_dir = os.path.dirname(os.path.abspath(__file__))
            project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_dir)))
            airfoil_data_dir = os.path.join(project_root, 'config', 'data', 'aerofoil data')

            if os.path.exists(airfoil_data_dir):
                self.airfoil_database = create_database_with_path(airfoil_data_dir)
                print(f"✅ 翼型数据库初始化成功")
                print(f"   数据路径: {airfoil_data_dir}")
                print(f"   目标翼型: {self.airfoil_name}")

                # 检查目标翼型是否可用
                available_airfoils = self.airfoil_database.get_available_airfoils()
                if self.airfoil_name in available_airfoils:
                    print(f"   ✅ 翼型 {self.airfoil_name} 数据加载成功")
                    airfoil_info = self.airfoil_database.get_airfoil_info(self.airfoil_name)
                    print(f"   雷诺数范围: {airfoil_info.get('reynolds_numbers', [])}")
                else:
                    print(f"   ⚠️ 翼型 {self.airfoil_name} 数据未找到，将使用理论模型")
                    print(f"   可用翼型: {available_airfoils}")
            else:
                print(f"⚠️ 翼型数据目录不存在: {airfoil_data_dir}")
                print("   将使用理论翼型模型")

        except Exception as e:
            print(f"⚠️ 翼型数据库初始化失败: {e}")
            print("   将使用理论翼型模型")
            self.airfoil_database = None

    def _get_chord(self, r: float) -> float:
        """获取指定径向位置的弦长"""
        return self._get_local_chord(r)
