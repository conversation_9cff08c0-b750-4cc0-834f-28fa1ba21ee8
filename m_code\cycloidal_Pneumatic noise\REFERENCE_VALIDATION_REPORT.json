{"timestamp": "2025-08-05T16:34:08.175657", "total_tests": 5, "passed_tests": 2, "failed_tests": 0, "pass_rate": 40.0, "total_execution_time": 0.20305109024047852, "validation_results": {"caradonna_tung": {"status": "skipped", "error": "求解器模块不可用，无法执行验证"}, "hart_ii": {"status": "skipped", "error": "求解器模块不可用，无法执行验证"}, "grid_convergence": {"status": "skipped", "error": "求解器模块不可用，无法执行验证"}, "bemt_basic": {"status": "passed", "execution_time": 0.10085439682006836, "test_case_config": "bemt_basic"}, "cycloidal_basic": {"status": "passed", "execution_time": 0.1007227897644043, "test_case_config": "cycloidal_basic"}}}